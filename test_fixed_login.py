#!/usr/bin/env python3
"""
Test script to verify the fixed login flow
"""

import requests
import json

def test_fixed_login_flow():
    """Test the complete login flow with the fixes applied"""
    
    # Step 1: Login request (same as frontend authApi.login)
    login_url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    
    credentials = {
        "email": "<EMAIL>",
        "password": "Dick920815##"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Origin": "http://localhost:3000"
    }
    
    try:
        print("=== Testing Fixed Login Flow ===")
        print(f"URL: {login_url}")
        print(f"Credentials: {credentials['email']}")
        
        response = requests.post(login_url, json=credentials, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Login failed!")
            print(f"Response: {response.text}")
            return False
        
        # Parse response (simulate handleApiResponse)
        backend_data = response.json()
        print(f"Backend response structure: {list(backend_data.keys())}")
        
        # Simulate authApi.login transformation
        if 'data' in backend_data:
            result_data = backend_data['data']
            
            # This is what authApi.login now returns after the fix
            api_response = {
                "success": True,
                "data": {
                    "token": result_data.get('token'),
                    "refresh_token": result_data.get('refresh_token'),
                    "expires_in": result_data.get('expires_in'),
                    "token_type": result_data.get('token_type'),
                    "user": result_data.get('user'),
                    "permissions": result_data.get('user', {}).get('permissions', []),
                    "roles": result_data.get('user', {}).get('roles', [])
                },
                "message": result_data.get('message')
            }
            
            print("✅ authApi.login response structure:")
            print(f"  success: {api_response['success']}")
            print(f"  data keys: {list(api_response['data'].keys())}")
            print(f"  token: {api_response['data']['token'][:50] if api_response['data']['token'] else 'None'}...")
            print(f"  refresh_token: {api_response['data']['refresh_token'][:50] if api_response['data']['refresh_token'] else 'None'}...")
            print(f"  expires_in: {api_response['data']['expires_in']}")
            print(f"  permissions: {api_response['data']['permissions']}")
            print(f"  roles: {api_response['data']['roles']}")
            
            # Simulate userAdapter.transformAuthResponse
            auth_data = api_response['data']
            transformed_auth = {
                "token": auth_data['token'],
                "refreshToken": auth_data['refresh_token'],  # Note: camelCase conversion
                "expiresIn": auth_data['expires_in'],        # Note: camelCase conversion
                "tokenType": auth_data['token_type'] or 'Bearer',
                "user": auth_data['user'],  # Would be transformed by transformItem
                "permissions": auth_data['permissions'],
                "roles": auth_data['roles']
            }
            
            print("\n✅ userAdapter.transformAuthResponse result:")
            print(f"  token: {transformed_auth['token'][:50] if transformed_auth['token'] else 'None'}...")
            print(f"  refreshToken: {transformed_auth['refreshToken'][:50] if transformed_auth['refreshToken'] else 'None'}...")
            print(f"  expiresIn: {transformed_auth['expiresIn']}")
            print(f"  permissions: {transformed_auth['permissions']}")
            print(f"  roles: {transformed_auth['roles']}")
            
            # Check for required fields
            required_fields = ['token', 'refreshToken', 'expiresIn', 'user']
            missing_fields = [field for field in required_fields if not transformed_auth.get(field)]
            
            if missing_fields:
                print(f"❌ Missing required fields after transformation: {missing_fields}")
                return False
            
            print("\n✅ All required fields present!")
            print("✅ Login flow should now work correctly!")
            
            # Simulate auth store state update
            print("\n=== Simulating Auth Store Update ===")
            print("✅ token.value = transformed_auth.token")
            print("✅ refreshToken.value = transformed_auth.refreshToken")
            print("✅ user.value = transformed_auth.user")
            print("✅ permissions.value = transformed_auth.permissions")
            print("✅ roles.value = transformed_auth.roles")
            print("✅ isLoggedIn.value = true")
            print("✅ tokenExpiry calculated and stored")
            print("✅ Cookies and localStorage updated")
            
            return True
        else:
            print("❌ No data field in backend response")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_fixed_login_flow()
    
    if success:
        print("\n🎉 Login flow fix verified! The frontend should now work correctly.")
    else:
        print("\n❌ Login flow still has issues.")
