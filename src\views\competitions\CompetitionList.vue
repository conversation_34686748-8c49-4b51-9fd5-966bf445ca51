<template>
  <div class="competition-list">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <div class="header-content">
        <h1>Competition Management</h1>
        <p>Manage and monitor all competitions in the system</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="showCreateDialog = true">
          Create Competition
        </el-button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <StatsCard
        title="Total Competitions"
        :value="stats.totalCompetitions"
        :change="5.2"
        change-type="increase"
        change-period="vs last month"
        :icon="Trophy"
        color="primary"
        :loading="statsLoading"
      />
      <StatsCard
        title="Active Competitions"
        :value="stats.activeCompetitions"
        :change="2.1"
        change-type="increase"
        change-period="vs last month"
        :icon="Play"
        color="success"
        :loading="statsLoading"
      />
      <StatsCard
        title="Total Participants"
        :value="stats.totalParticipants"
        :change="-1.3"
        change-type="decrease"
        change-period="vs last month"
        :icon="Users"
        color="info"
        :loading="statsLoading"
      />
      <StatsCard
        title="Avg Participants"
        :value="stats.averageParticipants"
        :change="0.8"
        change-type="increase"
        change-period="vs last month"
        :icon="UserGroup"
        color="warning"
        :loading="statsLoading"
      />
    </div>

    <!-- Filters -->
    <el-card class="filters-card">
      <div class="filters">
        <el-select
          v-model="filters.status"
          placeholder="Status"
          clearable
          @change="handleFilterChange"
        >
          <el-option label="All Status" value="" />
          <el-option label="Draft" value="draft" />
          <el-option label="Published" value="published" />
          <el-option label="Registration Open" value="registration_open" />
          <el-option label="Active" value="active" />
          <el-option label="Completed" value="completed" />
        </el-select>

        <el-select
          v-model="filters.type"
          placeholder="Type"
          clearable
          @change="handleFilterChange"
        >
          <el-option label="All Types" value="" />
          <el-option label="Hackathon" value="hackathon" />
          <el-option label="Contest" value="contest" />
          <el-option label="Challenge" value="challenge" />
          <el-option label="Tournament" value="tournament" />
        </el-select>

        <el-select
          v-model="filters.category"
          placeholder="Category"
          clearable
          @change="handleFilterChange"
        >
          <el-option label="All Categories" value="" />
          <el-option label="Programming" value="programming" />
          <el-option label="Data Science" value="data_science" />
          <el-option label="AI/ML" value="ai_ml" />
          <el-option label="Web Development" value="web_development" />
        </el-select>

        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="To"
          start-placeholder="Start date"
          end-placeholder="End date"
          @change="handleFilterChange"
        />
      </div>
    </el-card>

    <!-- Data Table -->
    <DataTable
      title="Competitions"
      :data="competitions"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :actions="tableActions"
      @search="handleSearch"
      @refresh="loadCompetitions"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <!-- Custom column slots -->
      <template #column-status="{ value }">
        <el-tag :type="getStatusType(value)">
          {{ getStatusLabel(value) }}
        </el-tag>
      </template>

      <template #column-participants="{ row }">
        <span>{{ row.currentParticipants }} / {{ row.maxParticipants || '∞' }}</span>
      </template>

      <template #column-featured="{ value }">
        <el-icon v-if="value" color="#f56c6c">
          <Star />
        </el-icon>
      </template>

      <template #column-dates="{ row }">
        <div class="date-info">
          <div>{{ formatDate(row.startDate) }}</div>
          <div class="date-secondary">{{ formatDate(row.endDate) }}</div>
        </div>
      </template>
    </DataTable>

    <!-- Create Competition Dialog -->
    <el-dialog
      v-model="showCreateDialog"
      title="Create New Competition"
      width="800px"
      :before-close="handleCloseDialog"
    >
      <CompetitionForm
        @submit="handleCreateCompetition"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Trophy, Play, Users, UserGroup, Star } from '@element-plus/icons-vue'
import DataTable from '@/components/common/DataTable.vue'
import StatsCard from '@/components/common/StatsCard.vue'
import CompetitionForm from './CompetitionForm.vue'
import { competitionService } from '@/services/competitionService.js'

// Reactive data
const competitions = ref([])
const stats = ref({
  totalCompetitions: 0,
  activeCompetitions: 0,
  totalParticipants: 0,
  averageParticipants: 0
})
const loading = ref(false)
const statsLoading = ref(false)
const showCreateDialog = ref(false)

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  offset: 0,
  total_pages: 0,
  has_next: false,
  has_prev: false
})

const filters = reactive({
  status: '',
  type: '',
  category: '',
  dateRange: null,
  search: ''
})

// Table configuration
const tableColumns = [
  {
    key: 'title',
    label: 'Title',
    sortable: true,
    minWidth: 200
  },
  {
    key: 'type',
    label: 'Type',
    width: 120,
    formatter: (value) => value.charAt(0).toUpperCase() + value.slice(1)
  },
  {
    key: 'status',
    label: 'Status',
    width: 140
  },
  {
    key: 'category',
    label: 'Category',
    width: 140,
    formatter: (value) => value.replace('_', ' ').toUpperCase()
  },
  {
    key: 'participants',
    label: 'Participants',
    width: 120,
    align: 'center'
  },
  {
    key: 'dates',
    label: 'Duration',
    width: 160
  },
  {
    key: 'featured',
    label: 'Featured',
    width: 80,
    align: 'center'
  }
]

const tableActions = [
  {
    key: 'view',
    label: 'View',
    type: 'primary',
    handler: (row) => handleViewCompetition(row)
  },
  {
    key: 'edit',
    label: 'Edit',
    type: 'warning',
    handler: (row) => handleEditCompetition(row)
  },
  {
    key: 'delete',
    label: 'Delete',
    type: 'danger',
    handler: (row) => handleDeleteCompetition(row),
    visible: (row) => row.status === 'draft'
  }
]

// Methods
const loadCompetitions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...filters,
      dateRange: filters.dateRange ? {
        start: filters.dateRange[0].toISOString(),
        end: filters.dateRange[1].toISOString()
      } : undefined
    }

    const response = await competitionService.getCompetitions(params)
    
    if (response.success) {
      competitions.value = response.data
      pagination.value = response.pagination
    } else {
      ElMessage.error(response.error.message)
    }
  } catch (error) {
    ElMessage.error('Failed to load competitions')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  statsLoading.value = true
  try {
    const response = await competitionService.getCompetitionStats()
    
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
  } finally {
    statsLoading.value = false
  }
}

const handleSearch = (query) => {
  filters.search = query
  pagination.value.page = 1
  loadCompetitions()
}

const handleFilterChange = () => {
  pagination.value.page = 1
  loadCompetitions()
}

const handlePageChange = ({ page }) => {
  pagination.value.page = page
  loadCompetitions()
}

const handleSizeChange = ({ pageSize }) => {
  pagination.value.limit = pageSize
  pagination.value.page = 1
  loadCompetitions()
}

const handleCreateCompetition = async (competitionData) => {
  try {
    const response = await competitionService.createCompetition(competitionData)
    
    if (response.success) {
      ElMessage.success('Competition created successfully')
      showCreateDialog.value = false
      loadCompetitions()
      loadStats()
    } else {
      ElMessage.error(response.error.message)
    }
  } catch (error) {
    ElMessage.error('Failed to create competition')
  }
}

const handleViewCompetition = (competition) => {
  // Navigate to competition detail view
  console.log('View competition:', competition)
}

const handleEditCompetition = (competition) => {
  // Open edit dialog
  console.log('Edit competition:', competition)
}

const handleDeleteCompetition = async (competition) => {
  try {
    await ElMessageBox.confirm(
      'This will permanently delete the competition. Continue?',
      'Warning',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    )

    const response = await competitionService.deleteCompetition(competition.id)
    
    if (response.success) {
      ElMessage.success('Competition deleted successfully')
      loadCompetitions()
      loadStats()
    } else {
      ElMessage.error(response.error.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete competition')
    }
  }
}

const handleCloseDialog = (done) => {
  done()
}

// Utility methods
const getStatusType = (status) => {
  const statusTypes = {
    draft: '',
    published: 'info',
    registration_open: 'success',
    registration_closed: 'warning',
    active: 'primary',
    completed: 'info',
    cancelled: 'danger'
  }
  return statusTypes[status] || ''
}

const getStatusLabel = (status) => {
  return status.replace('_', ' ').toUpperCase()
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

// Initialize
onMounted(() => {
  loadCompetitions()
  loadStats()
})
</script>

<style lang="scss" scoped>
.competition-list {
  padding: 24px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .filters-card {
    margin-bottom: 24px;
    
    .filters {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      
      .el-select,
      .el-date-editor {
        min-width: 180px;
      }
    }
  }
  
  .date-info {
    .date-secondary {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }
  }
}

@media (max-width: 768px) {
  .competition-list {
    padding: 16px;
    
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .filters {
      flex-direction: column;
      
      .el-select,
      .el-date-editor {
        width: 100%;
      }
    }
  }
}
</style>
