/**
 * Camp Admin API Service
 * Handles competition routes, qualifications, credits, and admin operations
 */

import { api } from '../api.js'
import { handleApiResponse, handleApiError, buildQueryString, logApiCall } from '../../utils/apiHelpers.js'

const BASE_PATH = '/camp-admin'

/**
 * Get list of routes
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of routes to return (max: 100)
 * @param {number} params.offset - Number of routes to skip (default: 0)
 * @returns {Promise<Object>} Routes list response
 */
export const getRoutes = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/routes${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/routes`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get list of competitions
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of competitions to return (max: 1000)
 * @param {number} params.offset - Number of competitions to skip (default: 0)
 * @param {string} params.route_id - Filter by route ID
 * @param {string} params.status - Filter by competition status
 * @returns {Promise<Object>} Competitions list response
 */
export const getCompetitions = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/${queryString}`

    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Get list of qualifications
 * @param {Object} params - Query parameters
 * @param {string} params.competition_id - Filter by competition ID
 * @param {number} params.limit - Number of qualifications to return (max: 1000)
 * @param {number} params.offset - Number of qualifications to skip (default: 0)
 * @returns {Promise<Object>} Qualifications list response
 */
export const getQualifications = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/qualifications${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/qualifications`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Create a new qualification
 * @param {Object} data - Qualification data
 * @param {string} data.competition_id - Competition ID
 * @param {number} data.credit - Credit amount
 * @param {number} data.qualification_type - Qualification type
 * @param {number} data.qualification_logic - Qualification logic
 * @param {string} data.qualification_name - Qualification name
 * @param {string} data.related_task_id - Related task ID
 * @param {number} data.score_threshold - Score threshold
 * @returns {Promise<Object>} Created qualification response
 */
export const createQualification = async (data) => {
  try {
    const url = `${BASE_PATH}/qualifications`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/qualifications`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get credit history
 * @param {Object} params - Query parameters
 * @param {string} params.competition_id - Filter by competition ID
 * @param {string} params.user_id - Filter by user ID
 * @param {number} params.limit - Number of records to return (1-1000, default: 200)
 * @param {number} params.offset - Number of records to skip (default: 0)
 * @returns {Promise<Object>} Credit history response
 */
export const getCreditHistory = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/credits/history${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/credits/history`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Award credits to users
 * @param {Object} data - Credit award data
 * @param {string[]} data.user_ids - Array of user IDs
 * @param {string} data.competition_id - Competition ID
 * @param {string} data.qual_id - Qualification ID
 * @param {number} data.credit - Credit amount
 * @param {string} data.remark - Award remark
 * @returns {Promise<Object>} Award result response
 */
export const awardCredits = async (data) => {
  try {
    const url = `${BASE_PATH}/credits/award`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/credits/award`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Revoke credits
 * @param {Object} data - Credit revoke data
 * @param {string} data.record_id - Credit record ID to revoke
 * @returns {Promise<Object>} Revoke result response
 */
export const revokeCredits = async (data) => {
  try {
    const url = `${BASE_PATH}/credits/revoke`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/credits/revoke`, data, normalizedError)
    throw normalizedError
  }
}

/**
 * Get admin logs
 * @param {Object} params - Query parameters
 * @param {number} params.limit - Number of logs to return (1-100, default: 20)
 * @param {number} params.offset - Number of logs to skip (default: 0)
 * @returns {Promise<Object>} Admin logs response
 */
export const getAdminLogs = async (params = {}) => {
  try {
    const queryString = buildQueryString(params)
    const url = `${BASE_PATH}/logs${queryString}`
    
    logApiCall('GET', url, null, null)
    
    const response = await api.get(url)
    const result = handleApiResponse(response)
    
    logApiCall('GET', url, null, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('GET', `${BASE_PATH}/logs`, params, normalizedError)
    throw normalizedError
  }
}

/**
 * Create admin log
 * @param {Object} data - Admin log data
 * @param {number} data.action_type - Action type
 * @param {string} data.action_related_id - Related ID
 * @param {string} data.remark - Log remark
 * @returns {Promise<Object>} Created log response
 */
export const createAdminLog = async (data) => {
  try {
    const url = `${BASE_PATH}/logs/create`
    
    logApiCall('POST', url, data, null)
    
    const response = await api.post(url, data)
    const result = handleApiResponse(response)
    
    logApiCall('POST', url, data, result)
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    const normalizedError = handleApiError(error)
    logApiCall('POST', `${BASE_PATH}/logs/create`, data, normalizedError)
    throw normalizedError
  }
}

// Export all functions as a service object
export const campAdminApi = {
  getRoutes,
  getCompetitions,
  getQualifications,
  createQualification,
  getCreditHistory,
  awardCredits,
  revokeCredits,
  getAdminLogs,
  createAdminLog
}
