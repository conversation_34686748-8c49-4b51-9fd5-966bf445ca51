<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>欢迎回来, {{ authStore.user?.email || 'Admin' }}!</h1>
      <p class="dashboard-subtitle">
        Manage your community services from this central dashboard
      </p>
    </div>

    <!-- Quick Stats -->
    <div class="stats-grid">
      <el-card class="stat-card" v-loading="statsLoading">
        <div class="stat-content">
          <div class="stat-icon competitions">
            <el-icon><Trophy /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.competitions.toLocaleString() }}</h3>
            <p>Active Competitions</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" v-loading="statsLoading">
        <div class="stat-content">
          <div class="stat-icon credits">
            <el-icon><Coin /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.credits.toLocaleString() }}</h3>
            <p>Credits Distributed</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" v-loading="statsLoading">
        <div class="stat-content">
          <div class="stat-icon schools">
            <el-icon><School /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.schools.toLocaleString() }}</h3>
            <p>学校参与</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" v-loading="statsLoading">
        <div class="stat-content">
          <div class="stat-icon users">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.users.toLocaleString() }}</h3>
            <p>总用户数</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>Quick Actions</h2>
      <div class="actions-grid">
        <el-card class="action-card" @click="$router.push('/competitions/create')">
          <div class="action-content">
            <el-icon class="action-icon"><Plus /></el-icon>
            <h3>活动规则</h3>
            <p>添加，管理活动规则</p>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/credits')">
          <div class="action-content">
            <el-icon class="action-icon"><Coin /></el-icon>
            <h3>学分管理</h3>
            <p>分发学分，同步徽章</p>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/schools')">
          <div class="action-content">
            <el-icon class="action-icon"><DataAnalysis /></el-icon>
            <h3>数据统计</h3>
            <p>查看学校、赛道的数据统计</p>
          </div>
        </el-card>

        <el-card class="action-card" @click="$router.push('/admin')">
          <div class="action-content">
            <el-icon class="action-icon"><Tools /></el-icon>
            <h3>管理工具</h3>
            <p>其他管理工具</p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
      <h2>Recent Activity</h2>
      <el-card v-loading="activitiesLoading">
        <el-timeline v-if="recentActivities.length > 0">
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            {{ activity.description }}
          </el-timeline-item>
        </el-timeline>
        <el-empty
          v-else-if="!activitiesLoading"
          description="暂无最近活动记录"
          :image-size="100"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { dashboardService } from '@/services/dashboardService'
import { useDashboardLoading, LOADING_OPERATIONS } from '@/composables/useLoadingState'
import { ElMessage } from 'element-plus'
import {
  Trophy,
  Coin,
  School,
  User,
  Plus,
  DataAnalysis,
  Tools
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const {
  isLoading,
  loadingMessage,
  isOperationLoading,
  loadDashboardData,
  setOperationLoading
} = useDashboardLoading()

// Dashboard data
const stats = ref({
  competitions: 0,
  credits: 0,
  schools: 0,
  users: 0
})

const recentActivities = ref([])

// Computed loading states for different sections
const statsLoading = computed(() => isOperationLoading(LOADING_OPERATIONS.DASHBOARD_STATS))
const activitiesLoading = computed(() => isOperationLoading(LOADING_OPERATIONS.DASHBOARD_ACTIVITIES))

// Methods
const loadData = async () => {
  try {
    console.log('Loading dashboard data from real APIs...')

    // Load dashboard statistics and activities using the loading composable
    const dashboardData = await loadDashboardData(async () => {
      return await dashboardService.getDashboardData()
    })

    if (dashboardData.success) {
      stats.value = dashboardData.stats
      recentActivities.value = dashboardData.activities

      console.log('Dashboard data loaded successfully:', {
        stats: dashboardData.stats,
        activitiesCount: dashboardData.activities.length
      })
    } else {
      console.warn('Failed to load dashboard data, using fallback')
      // Fallback to default values if API fails
      stats.value = {
        competitions: 0,
        credits: 0,
        schools: 0,
        users: 0
      }
      recentActivities.value = []

      ElMessage.warning('部分数据加载失败，显示默认值')
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error)

    // Fallback to default values on error
    stats.value = {
      competitions: 0,
      credits: 0,
      schools: 0,
      users: 0
    }
    recentActivities.value = []

    ElMessage.error('数据加载失败，请刷新页面重试')
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;

  h1 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  .dashboard-subtitle {
    font-size: 16px;
    color: var(--text-color-secondary);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        color: white;
        
        &.competitions {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.credits {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.schools {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.users {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .stat-info {
        h3 {
          font-size: 28px;
          font-weight: 700;
          color: var(--text-color);
          margin-bottom: 4px;
        }

        p {
          font-size: 14px;
          color: var(--text-color-secondary);
        }
      }
    }
  }
}

.quick-actions {
  margin-bottom: 40px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 20px;
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    
    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px var(--shadow-color);
        border-color: var(--sidebar-active);
      }
      
      .action-content {
        text-align: center;
        padding: 20px;
        
        .action-icon {
          font-size: 48px;
          color: var(--sidebar-active);
          margin-bottom: 16px;
        }

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-color);
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: var(--text-color-secondary);
          line-height: 1.5;
        }
      }
    }
  }
}

.recent-activity {
  h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 20px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
}
</style>
