#!/usr/bin/env python3
"""
Test script to verify login functionality
"""

import requests
import json

def test_login():
    """Test the login endpoint with provided credentials"""
    
    url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    
    credentials = {
        "email": "<PERSON><PERSON><PERSON><PERSON>@heywhale.com",
        "password": "Dick920815##"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Testing login at: {url}")
        print(f"Credentials: {credentials['email']}")
        
        response = requests.post(url, json=credentials, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print("❌ Login failed!")
            try:
                error_data = response.json()
                print(f"Error Response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    test_login()
