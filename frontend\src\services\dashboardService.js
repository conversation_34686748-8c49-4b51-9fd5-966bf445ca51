/**
 * Dashboard Service
 * Aggregates data from multiple APIs for dashboard display
 */

import { analyticsApi } from './api/analyticsApi.js'
import { campAdminApi } from './api/campAdminApi.js'
import { communityApi } from './api/communityApi.js'
import { errorHandler, createResilientApiWrapper, RETRY_CONFIGS } from './errorHandler.js'
import { isMockEnabled } from './mockApiHandler.js'
import cacheService, { CACHE_TTL } from './cacheService.js'
import requestOptimizer, { optimizedFetch, batchFetch } from './requestOptimizer.js'

// Check if we're using mock APIs
const MOCK_ENABLED = isMockEnabled()

// Mock fallback data for when APIs fail
const MOCK_STATS = {
  competitions: 12,
  credits: 15420,
  schools: 85,
  users: 2340
}

const MOCK_ACTIVITIES = [
  {
    id: 1,
    description: 'New competition "Summer Challenge 2024" created',
    timestamp: '2024-01-15 10:30',
    type: 'primary'
  },
  {
    id: 2,
    description: 'Credits distributed to 150 users',
    timestamp: '2024-01-15 09:15',
    type: 'success'
  },
  {
    id: 3,
    description: 'School statistics updated for Beijing University',
    timestamp: '2024-01-15 08:45',
    type: 'info'
  },
  {
    id: 4,
    description: 'Badge synchronization completed',
    timestamp: '2024-01-14 16:20',
    type: 'warning'
  }
]

/**
 * Get dashboard statistics from multiple APIs
 * @returns {Promise<Object>} Dashboard statistics
 */
export const getDashboardStats = async () => {
  const cacheKey = 'dashboard:stats'

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
      console.log(`📊 Loading dashboard stats (Mock: ${MOCK_ENABLED})`)

      const stats = {
        competitions: 0,
        credits: 0,
        schools: 0,
        users: 0
      }

    // Use batch requests for better performance
    const batchRequests = [
      {
        url: '/analytics/count/competitions',
        requestFn: () => getCompetitionCount(),
        options: { cache: true, cacheTTL: CACHE_TTL.COMPETITION_COUNT }
      },
      {
        url: '/analytics/credits/total',
        requestFn: () => getTotalCreditsDistributed(),
        options: { cache: true, cacheTTL: CACHE_TTL.DASHBOARD_STATS }
      },
      {
        url: '/analytics/count/schools',
        requestFn: () => getSchoolCount(),
        options: { cache: true, cacheTTL: CACHE_TTL.SCHOOL_COUNT }
      },
      {
        url: '/analytics/count/users',
        requestFn: () => getUserCount(),
        options: { cache: true, cacheTTL: CACHE_TTL.USER_COUNT }
      }
    ]

    const [
      competitionsResult,
      creditsResult,
      schoolsResult,
      usersResult
    ] = await batchFetch(batchRequests)

    // Handle batch results with fallbacks
    stats.competitions = competitionsResult !== null ? competitionsResult : MOCK_STATS.competitions
    stats.credits = creditsResult !== null ? creditsResult : MOCK_STATS.credits
    stats.schools = schoolsResult !== null ? schoolsResult : MOCK_STATS.schools
    stats.users = usersResult !== null ? usersResult : MOCK_STATS.users

    // Log any failures
    if (competitionsResult === null) console.warn('Failed to load competitions count')
    if (creditsResult === null) console.warn('Failed to load credits count')
    if (schoolsResult === null) console.warn('Failed to load schools count')
    if (usersResult === null) console.warn('Failed to load users count')

    console.log('📊 Dashboard stats loaded:', stats)

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Error loading dashboard stats:', error)
    // Return mock data as fallback
    return {
      success: true,
      data: MOCK_STATS
    }
  }
  }, CACHE_TTL.DASHBOARD_STATS)
}

/**
 * Get total competition count using new count endpoint
 * @returns {Promise<number>} Competition count
 */
const getCompetitionCount = async () => {
  const cacheKey = 'count:competitions'

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
      // Try new count endpoint first
      try {
        const response = await analyticsApi.getCount('competitions')
        if (response.success && response.data) {
          return response.data.count || 0
        }
      } catch (countError) {
        console.warn('Count endpoint failed, falling back to legacy method:', countError)
      }

    // Fallback to legacy method
    const response = await campAdminApi.getCompetitions({ limit: 1 })
    if (response.success && response.data) {
      // If the API returns total count in metadata, use that
      // Otherwise, we need to make a call to get all competitions
      const allCompetitions = await campAdminApi.getCompetitions({ limit: 1000 })
      return allCompetitions.success ? allCompetitions.data.length : 0
    }
    return 0
  } catch (error) {
    console.error('Error getting competition count:', error)
    // Use enhanced error handling for better user experience
    const classification = errorHandler.classifyError(error)
    if (classification.shouldRetry) {
      console.log('Competition count error is retryable, will be handled by retry service')
    }
    return 0
  }
  }, CACHE_TTL.COMPETITION_COUNT)
}

/**
 * Get total credits distributed
 * @returns {Promise<number>} Total credits distributed
 */
const getTotalCreditsDistributed = async () => {
  try {
    const response = await campAdminApi.getCreditHistory({ limit: 1000 })
    if (response.success && response.data) {
      // Sum up all credit amounts from the history
      const totalCredits = response.data.reduce((sum, record) => {
        return sum + (record.credit || 0)
      }, 0)
      return totalCredits
    }
    return 0
  } catch (error) {
    console.error('Error getting total credits:', error)
    return 0
  }
}

/**
 * Get total school/university count using new count endpoint
 * @returns {Promise<number>} School count
 */
const getSchoolCount = async () => {
  const cacheKey = 'count:schools'

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
      // Try new count endpoint first
      try {
        const response = await analyticsApi.getCount('schools')
        if (response.success && response.data) {
          return response.data.count || 0
        }
      } catch (countError) {
        console.warn('Count endpoint failed, falling back to legacy method:', countError)
      }

    // Fallback to legacy method
    const response = await communityApi.getUniversities({ limit: 1 })
    if (response.success && response.total !== undefined) {
      return response.total
    }
    // Fallback: get all universities and count them
    const allUniversities = await communityApi.getAllUniversities()
    return allUniversities.success ? allUniversities.data.length : 0
  } catch (error) {
    console.error('Error getting school count:', error)
    return 0
  }
  }, CACHE_TTL.SCHOOL_COUNT)
}

/**
 * Get total user count using new count endpoint
 * @returns {Promise<number>} User count
 */
const getUserCount = async () => {
  const cacheKey = 'count:users'

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
      // Try new count endpoint first
      try {
        const response = await analyticsApi.getCount('users')
        if (response.success && response.data) {
          return response.data.count || 0
        }
      } catch (countError) {
        console.warn('Count endpoint failed, falling back to legacy method:', countError)
      }

    // Fallback to legacy method
    const response = await analyticsApi.getTotalUserRankings({ page_size: 1, current_page: 1 })
    if (response.success && response.total !== undefined) {
      return response.total
    }
    return 0
  } catch (error) {
    console.error('Error getting user count:', error)
    return 0
  }
  }, CACHE_TTL.USER_COUNT)
}

/**
 * Get recent admin activities
 * @param {number} limit - Number of activities to return (default: 10)
 * @returns {Promise<Object>} Recent activities
 */
export const getRecentActivities = async (limit = 10) => {
  const cacheKey = cacheService.generateKey('activities:recent', { limit })

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
    console.log(`📋 Loading recent activities (Mock: ${MOCK_ENABLED})`)

    const response = await campAdminApi.getAdminLogs({ limit, offset: 0 })

    if (response.success && response.data && response.data.length > 0) {
      // Transform admin logs to activity format
      const activities = response.data.map((log, index) => ({
        id: log.id || `activity_${index}`,
        description: formatActivityDescription(log),
        timestamp: formatTimestamp(log.created_at || log.timestamp),
        type: getActivityType(log.action_type)
      }))

      console.log(`📋 Loaded ${activities.length} activities from API`)

      return {
        success: true,
        data: activities
      }
    }

    // Fallback to mock data if no real activities found
    console.log('📋 No activities found, using mock data')
    return {
      success: true,
      data: MOCK_ACTIVITIES.slice(0, limit)
    }
  } catch (error) {
    console.error('Error loading recent activities:', error)
    // Return mock data as fallback
    console.log('📋 API failed, using mock activities')
    return {
      success: true,
      data: MOCK_ACTIVITIES.slice(0, limit)
    }
  }
  }, CACHE_TTL.ACTIVITIES)
}

/**
 * Format activity description based on admin log data
 * @param {Object} log - Admin log entry
 * @returns {string} Formatted description
 */
const formatActivityDescription = (log) => {
  const actionType = log.action_type
  const remark = log.remark || ''
  const relatedId = log.action_related_id || ''

  // Map action types to user-friendly descriptions
  const actionDescriptions = {
    1: 'Competition created',
    2: 'Credits awarded',
    3: 'Credits revoked',
    4: 'Qualification created',
    5: 'User management action',
    6: 'System configuration updated'
  }

  const baseDescription = actionDescriptions[actionType] || 'Admin action performed'
  
  if (remark) {
    return `${baseDescription}: ${remark}`
  } else if (relatedId) {
    return `${baseDescription} (ID: ${relatedId})`
  }
  
  return baseDescription
}

/**
 * Format timestamp for display
 * @param {string} timestamp - ISO timestamp or date string
 * @returns {string} Formatted timestamp
 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timestamp
  }
}

/**
 * Get activity type for timeline display
 * @param {number} actionType - Action type from admin log
 * @returns {string} Timeline type
 */
const getActivityType = (actionType) => {
  const typeMap = {
    1: 'primary',    // Competition created
    2: 'success',    // Credits awarded
    3: 'warning',    // Credits revoked
    4: 'info',       // Qualification created
    5: 'primary',    // User management
    6: 'info'        // System config
  }
  
  return typeMap[actionType] || 'primary'
}

/**
 * Get dashboard summary data using new single endpoint
 * @param {string} userId - Optional user ID for user-specific context
 * @returns {Promise<Object>} Complete dashboard data
 */
export const getDashboardData = async (userId = null) => {
  const cacheKey = cacheService.generateKey('dashboard:data', { userId })

  return await cacheService.getOrSet(cacheKey, async () => {
    try {
      console.log(`📊 Loading dashboard data using new endpoint (Mock: ${MOCK_ENABLED})`)

    // Try to use the new single dashboard endpoint first
    try {
      const dashboardResponse = await analyticsApi.getDashboard(userId)

      if (dashboardResponse.success && dashboardResponse.data) {
        const dashboardData = dashboardResponse.data

        // Transform the new dashboard data structure to match expected format
        const result = {
          success: true,
          stats: {
            competitions: extractStatValue(dashboardData.summary_statistics, 'total_competitions'),
            credits: extractStatValue(dashboardData.summary_statistics, 'total_credits'),
            schools: extractStatValue(dashboardData.summary_statistics, 'total_schools'),
            users: extractStatValue(dashboardData.summary_statistics, 'total_users')
          },
          activities: transformUserActivity(dashboardData.user_activity),
          topSchools: dashboardData.top_schools || [],
          routeRankings: dashboardData.route_rankings || [],
          generationTime: dashboardData.display_generation_time
        }

        console.log('📊 Dashboard data loaded from new endpoint:', result)
        return result
      }
    } catch (dashboardError) {
      console.warn('New dashboard endpoint failed, falling back to legacy method:', dashboardError)
    }

    // Fallback to legacy method if new endpoint fails
    const [statsResult, activitiesResult] = await Promise.allSettled([
      getDashboardStats(),
      getRecentActivities(5)
    ])

    const result = {
      success: true,
      stats: { competitions: 0, credits: 0, schools: 0, users: 0 },
      activities: []
    }

    if (statsResult.status === 'fulfilled' && statsResult.value.success) {
      result.stats = statsResult.value.data
    }

    if (activitiesResult.status === 'fulfilled' && activitiesResult.value.success) {
      result.activities = activitiesResult.value.data
    }

    console.log('📊 Dashboard data loaded using legacy method:', result)
    return result
  } catch (error) {
    console.error('Error loading dashboard data:', error)
    return errorHandler.handleError(error, 'Failed to load dashboard data')
  }
  }, CACHE_TTL.DASHBOARD_STATS)
}

/**
 * Extract statistic value from summary statistics array
 * @param {Array} summaryStats - Array of summary statistics
 * @param {string} statName - Name of the statistic to extract
 * @returns {number} Statistic value
 */
const extractStatValue = (summaryStats, statName) => {
  if (!Array.isArray(summaryStats)) return 0

  const stat = summaryStats.find(s => s.statistics_name === statName)
  return stat ? stat.statistics_value : 0
}

/**
 * Transform user activity data to activities format
 * @param {Object} userActivity - User activity data from backend
 * @returns {Array} Transformed activities array
 */
const transformUserActivity = (userActivity) => {
  if (!userActivity || typeof userActivity !== 'object') return []

  // Transform backend user activity structure to frontend activities format
  // This is a placeholder - adjust based on actual backend structure
  const activities = []

  if (userActivity.recent_activities && Array.isArray(userActivity.recent_activities)) {
    userActivity.recent_activities.forEach((activity, index) => {
      activities.push({
        id: activity.id || `activity_${index}`,
        description: activity.description || activity.action || 'Activity',
        timestamp: activity.timestamp || activity.created_at,
        type: activity.type || 'primary'
      })
    })
  }

  return activities.slice(0, 5) // Limit to 5 activities
}

// Export service object
export const dashboardService = {
  getDashboardStats,
  getRecentActivities,
  getDashboardData
}
