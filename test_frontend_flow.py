#!/usr/bin/env python3
"""
Test script to simulate the exact frontend login flow
"""

import requests
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def simulate_frontend_login():
    """Simulate the exact frontend login flow"""
    
    # Step 1: Login request (same as frontend)
    login_url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    
    credentials = {
        "email": "<EMAIL>",
        "password": "Dick920815##"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Origin": "http://localhost:3000"
    }
    
    try:
        print("=== Step 1: Login Request ===")
        print(f"URL: {login_url}")
        print(f"Credentials: {credentials['email']}")
        
        response = requests.post(login_url, json=credentials, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Login failed!")
            print(f"Response: {response.text}")
            return False
        
        login_data = response.json()
        print("✅ Login successful!")
        print(f"Response structure: {list(login_data.keys())}")
        
        # Extract token data (simulate userAdapter.transformAuthResponse)
        if 'data' in login_data:
            auth_data = login_data['data']
            print(f"Auth data keys: {list(auth_data.keys())}")
            
            # Check for required fields
            required_fields = ['token', 'refresh_token', 'expires_in', 'user']
            missing_fields = [field for field in required_fields if field not in auth_data]
            
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False
            
            token = auth_data['token']
            refresh_token = auth_data['refresh_token']
            expires_in = auth_data['expires_in']
            user = auth_data['user']
            
            print(f"✅ Token extracted: {token[:50]}...")
            print(f"✅ Refresh token extracted: {refresh_token[:50]}...")
            print(f"✅ Expires in: {expires_in} seconds")
            print(f"✅ User data: {user}")
            
            # Step 2: Test token verification (simulate router guard)
            print("\n=== Step 2: Token Verification ===")
            verify_url = "http://localhost:8005/api/v1/camp-admin/auth/verify"
            
            verify_headers = {
                "Authorization": f"Bearer {token}",
                "Origin": "http://localhost:3000"
            }
            
            verify_response = requests.get(verify_url, headers=verify_headers)
            print(f"Verify Status Code: {verify_response.status_code}")
            
            if verify_response.status_code == 200:
                verify_data = verify_response.json()
                print("✅ Token verification successful!")
                print(f"Verify response: {verify_data}")
                
                # Step 3: Test protected endpoint access
                print("\n=== Step 3: Protected Endpoint Access ===")
                protected_url = "http://localhost:8005/api/v1/camp-admin/routes"
                
                protected_response = requests.get(protected_url, headers=verify_headers)
                print(f"Protected endpoint status: {protected_response.status_code}")
                
                if protected_response.status_code == 200:
                    print("✅ Protected endpoint access successful!")
                    print("🎉 Complete login flow working!")
                    return True
                else:
                    print(f"❌ Protected endpoint failed: {protected_response.text}")
                    return False
            else:
                print(f"❌ Token verification failed: {verify_response.text}")
                return False
        else:
            print("❌ No data field in login response")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_auth_state_persistence():
    """Test if auth state would persist correctly"""
    print("\n=== Step 4: Auth State Persistence Test ===")
    
    # Simulate localStorage storage (what frontend does)
    print("✅ Simulating localStorage storage...")
    print("- auth_token: stored")
    print("- auth_refresh_token: stored") 
    print("- auth_user: stored")
    print("- auth_token_expiry: stored")
    
    # Simulate page refresh / router guard check
    print("✅ Simulating page refresh / router guard...")
    print("- Auth state would be restored from localStorage")
    print("- Token expiry would be checked")
    print("- User would remain logged in")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Complete Frontend Login Flow")
    print("=" * 50)
    
    success = simulate_frontend_login()
    
    if success:
        test_auth_state_persistence()
        print("\n🎉 All tests passed! Login flow should work correctly.")
    else:
        print("\n❌ Login flow has issues that need to be resolved.")
