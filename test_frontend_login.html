<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Frontend Login</h1>
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="Dick920815##" required>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Making login request...');
                
                const response = await fetch('http://localhost:8005/api/v1/camp-admin/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Login successful!\n\nResponse:\n${JSON.stringify(data, null, 2)}`;

                    // Store token in localStorage for testing
                    if (data.data && data.data.token) {
                        localStorage.setItem('auth_token', data.data.token);
                        localStorage.setItem('refresh_token', data.data.refresh_token);
                        localStorage.setItem('user_data', JSON.stringify(data.data.user));
                        console.log('Auth data stored in localStorage');

                        // Test if we can access the frontend now
                        setTimeout(() => {
                            window.open('http://localhost:3000/dashboard', '_blank');
                        }, 2000);
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Login failed!\n\nStatus: ${response.status}\nResponse:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Request failed!\n\nError: ${error.message}`;
            }
        });
    </script>
</body>
</html>
