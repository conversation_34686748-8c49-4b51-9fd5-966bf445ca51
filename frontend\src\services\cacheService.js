/**
 * Cache Service
 * Provides in-memory caching with TTL support for API responses
 */

import { envConfig } from '@/utils/envConfig'

class CacheService {
  constructor() {
    this.cache = new Map()
    this.enabled = envConfig.cache.enabled
    this.defaultTTL = envConfig.cache.ttl || 300000 // 5 minutes default
    
    console.log(`🗄️ Cache Service initialized (enabled: ${this.enabled}, TTL: ${this.defaultTTL}ms)`)
  }

  /**
   * Generate cache key from URL and parameters
   * @param {string} key - Base cache key
   * @param {Object} params - Optional parameters to include in key
   * @returns {string} Generated cache key
   */
  generateKey(key, params = null) {
    if (!params) return key
    
    const paramString = JSON.stringify(params, Object.keys(params).sort())
    return `${key}:${btoa(paramString)}`
  }

  /**
   * Set cache entry with TTL
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - Time to live in milliseconds (optional)
   */
  set(key, data, ttl = null) {
    if (!this.enabled) return

    const expiresAt = Date.now() + (ttl || this.defaultTTL)
    
    this.cache.set(key, {
      data,
      expiresAt,
      createdAt: Date.now()
    })

    console.log(`🗄️ Cached data for key: ${key} (expires in ${(ttl || this.defaultTTL) / 1000}s)`)
  }

  /**
   * Get cache entry if not expired
   * @param {string} key - Cache key
   * @returns {any|null} Cached data or null if not found/expired
   */
  get(key) {
    if (!this.enabled) return null

    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      console.log(`🗄️ Cache expired for key: ${key}`)
      return null
    }

    const age = Date.now() - entry.createdAt
    console.log(`🗄️ Cache hit for key: ${key} (age: ${age}ms)`)
    return entry.data
  }

  /**
   * Check if cache has valid entry for key
   * @param {string} key - Cache key
   * @returns {boolean} True if valid cache entry exists
   */
  has(key) {
    return this.get(key) !== null
  }

  /**
   * Delete cache entry
   * @param {string} key - Cache key
   */
  delete(key) {
    const deleted = this.cache.delete(key)
    if (deleted) {
      console.log(`🗄️ Deleted cache entry: ${key}`)
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  clear() {
    const size = this.cache.size
    this.cache.clear()
    console.log(`🗄️ Cleared ${size} cache entries`)
  }

  /**
   * Clear expired cache entries
   */
  clearExpired() {
    const now = Date.now()
    let cleared = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key)
        cleared++
      }
    }
    
    if (cleared > 0) {
      console.log(`🗄️ Cleared ${cleared} expired cache entries`)
    }
    
    return cleared
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const now = Date.now()
    let expired = 0
    let valid = 0
    
    for (const entry of this.cache.values()) {
      if (now > entry.expiresAt) {
        expired++
      } else {
        valid++
      }
    }
    
    return {
      total: this.cache.size,
      valid,
      expired,
      enabled: this.enabled,
      defaultTTL: this.defaultTTL
    }
  }

  /**
   * Wrapper for async functions with caching
   * @param {string} key - Cache key
   * @param {Function} asyncFn - Async function to execute if cache miss
   * @param {number} ttl - Time to live in milliseconds (optional)
   * @returns {Promise<any>} Cached or fresh data
   */
  async getOrSet(key, asyncFn, ttl = null) {
    // Try to get from cache first
    const cached = this.get(key)
    if (cached !== null) {
      return cached
    }

    // Cache miss - execute function and cache result
    try {
      const result = await asyncFn()
      this.set(key, result, ttl)
      return result
    } catch (error) {
      // Don't cache errors
      throw error
    }
  }

  /**
   * Invalidate cache entries by pattern
   * @param {string|RegExp} pattern - Pattern to match keys
   */
  invalidatePattern(pattern) {
    let deleted = 0
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key)
        deleted++
      }
    }
    
    if (deleted > 0) {
      console.log(`🗄️ Invalidated ${deleted} cache entries matching pattern: ${pattern}`)
    }
    
    return deleted
  }
}

// Create singleton instance
const cacheService = new CacheService()

// Set up periodic cleanup of expired entries (every 5 minutes)
if (cacheService.enabled) {
  setInterval(() => {
    cacheService.clearExpired()
  }, 300000) // 5 minutes
}

export default cacheService

// Export specific cache TTL constants for different data types
export const CACHE_TTL = {
  DASHBOARD_STATS: 300000,    // 5 minutes
  USER_COUNT: 600000,         // 10 minutes  
  SCHOOL_COUNT: 1800000,      // 30 minutes
  COMPETITION_COUNT: 300000,  // 5 minutes
  ACTIVITIES: 60000,          // 1 minute
  ROUTES: 3600000,           // 1 hour
  QUALIFICATIONS: 300000     // 5 minutes
}
