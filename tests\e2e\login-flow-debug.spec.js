/**
 * <PERSON><PERSON> test to debug the login flow and redirection issues
 */

import { test, expect } from '@playwright/test'

test.describe('Login Flow Debug', () => {
  
  test('should debug complete login flow with detailed logging', async ({ page }) => {
    // Enable console logging
    page.on('console', msg => {
      console.log(`[BROWSER CONSOLE] ${msg.type()}: ${msg.text()}`)
    })
    
    // Enable request/response logging
    page.on('request', request => {
      console.log(`[REQUEST] ${request.method()} ${request.url()}`)
    })
    
    page.on('response', response => {
      console.log(`[RESPONSE] ${response.status()} ${response.url()}`)
    })
    
    // Enable error logging
    page.on('pageerror', error => {
      console.log(`[PAGE ERROR] ${error.message}`)
    })
    
    console.log('=== Starting Login Flow Debug ===')
    
    // Step 1: Navigate to login page
    console.log('Step 1: Navigating to login page...')
    await page.goto('http://localhost:3000/login')
    await page.waitForLoadState('networkidle')
    
    // Take screenshot of login page
    await page.screenshot({ path: 'tests/screenshots/01-login-page.png', fullPage: true })
    
    // Check if login form is present
    const loginForm = page.locator('.login-form, form')
    await expect(loginForm).toBeVisible()
    console.log('✅ Login form is visible')
    
    // Step 2: Fill in credentials
    console.log('Step 2: Filling in credentials...')
    const emailInput = page.locator('input[type="email"], input[placeholder*="email" i]')
    const passwordInput = page.locator('input[type="password"]')
    
    await emailInput.fill('<EMAIL>')
    await passwordInput.fill('Dick920815##')
    
    await page.screenshot({ path: 'tests/screenshots/02-credentials-filled.png', fullPage: true })
    console.log('✅ Credentials filled')
    
    // Step 3: Check auth store state before login
    console.log('Step 3: Checking auth store state before login...')
    const authStateBefore = await page.evaluate(() => {
      return {
        localStorage: { ...localStorage },
        cookies: document.cookie,
        authStore: window.__VUE_DEVTOOLS_GLOBAL_HOOK__ ? 'Available' : 'Not Available'
      }
    })
    console.log('Auth state before login:', authStateBefore)
    
    // Step 4: Submit login form
    console.log('Step 4: Submitting login form...')
    const submitButton = page.locator('button[type="submit"], .el-button--primary')
    
    // Listen for navigation events
    let navigationOccurred = false
    page.on('framenavigated', frame => {
      if (frame === page.mainFrame()) {
        navigationOccurred = true
        console.log(`[NAVIGATION] Navigated to: ${frame.url()}`)
      }
    })
    
    // Click submit and wait for response
    await Promise.all([
      page.waitForResponse(response => 
        response.url().includes('/auth/login') && response.status() === 200
      ),
      submitButton.click()
    ])
    
    console.log('✅ Login request completed')
    await page.screenshot({ path: 'tests/screenshots/03-after-login-click.png', fullPage: true })
    
    // Step 5: Wait a bit and check what happened
    console.log('Step 5: Waiting and checking post-login state...')
    await page.waitForTimeout(2000)
    
    const currentUrl = page.url()
    console.log(`Current URL after login: ${currentUrl}`)
    
    // Check auth store state after login
    const authStateAfter = await page.evaluate(() => {
      return {
        localStorage: { ...localStorage },
        cookies: document.cookie,
        currentUrl: window.location.href,
        authStoreData: window.localStorage.getItem('user_data'),
        authToken: window.localStorage.getItem('auth_token') || 'Cookie: ' + document.cookie.split('auth_token=')[1]?.split(';')[0]
      }
    })
    console.log('Auth state after login:', authStateAfter)
    
    await page.screenshot({ path: 'tests/screenshots/04-post-login-state.png', fullPage: true })
    
    // Step 6: Check if we're on dashboard or still on login
    if (currentUrl.includes('/login')) {
      console.log('❌ Still on login page - investigating why...')
      
      // Check for error messages
      const errorMessages = await page.locator('.el-message--error, .error-message, .alert-error').allTextContents()
      if (errorMessages.length > 0) {
        console.log('Error messages found:', errorMessages)
      }
      
      // Check network tab for failed requests
      console.log('Checking for failed network requests...')
      
      // Try to manually navigate to dashboard to see what happens
      console.log('Attempting manual navigation to dashboard...')
      await page.goto('http://localhost:3000/dashboard')
      await page.waitForTimeout(2000)
      
      const dashboardUrl = page.url()
      console.log(`URL after manual dashboard navigation: ${dashboardUrl}`)
      await page.screenshot({ path: 'tests/screenshots/05-manual-dashboard-nav.png', fullPage: true })
      
    } else if (currentUrl.includes('/dashboard')) {
      console.log('✅ Successfully redirected to dashboard')
      
      // Check if dashboard content is loaded
      const dashboardContent = page.locator('.dashboard, .el-main, main')
      if (await dashboardContent.count() > 0) {
        console.log('✅ Dashboard content is present')
      } else {
        console.log('⚠️ Dashboard URL reached but content not loaded')
      }
      
    } else {
      console.log(`⚠️ Unexpected URL: ${currentUrl}`)
    }
    
    // Step 7: Test page refresh behavior
    console.log('Step 7: Testing page refresh behavior...')
    await page.reload()
    await page.waitForTimeout(2000)
    
    const urlAfterRefresh = page.url()
    console.log(`URL after refresh: ${urlAfterRefresh}`)
    await page.screenshot({ path: 'tests/screenshots/06-after-refresh.png', fullPage: true })
    
    // Final auth state check
    const finalAuthState = await page.evaluate(() => {
      return {
        localStorage: { ...localStorage },
        cookies: document.cookie,
        currentUrl: window.location.href
      }
    })
    console.log('Final auth state:', finalAuthState)
    
    console.log('=== Login Flow Debug Complete ===')
  })
  
  test('should test router guard behavior', async ({ page }) => {
    console.log('=== Testing Router Guard Behavior ===')
    
    // Enable logging
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`)
    })
    
    // Try to access protected route without login
    console.log('Step 1: Accessing protected route without login...')
    await page.goto('http://localhost:3000/dashboard')
    await page.waitForTimeout(2000)
    
    const urlWithoutLogin = page.url()
    console.log(`URL when accessing dashboard without login: ${urlWithoutLogin}`)
    await page.screenshot({ path: 'tests/screenshots/07-dashboard-without-login.png', fullPage: true })
    
    // Now login and try again
    console.log('Step 2: Logging in first...')
    await page.goto('http://localhost:3000/login')
    
    await page.locator('input[type="email"]').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('Dick920815##')
    
    await Promise.all([
      page.waitForResponse(response => response.url().includes('/auth/login')),
      page.locator('button[type="submit"]').click()
    ])
    
    await page.waitForTimeout(2000)
    
    console.log('Step 3: Trying to access dashboard after login...')
    await page.goto('http://localhost:3000/dashboard')
    await page.waitForTimeout(2000)
    
    const urlAfterLogin = page.url()
    console.log(`URL when accessing dashboard after login: ${urlAfterLogin}`)
    await page.screenshot({ path: 'tests/screenshots/08-dashboard-after-login.png', fullPage: true })
    
    console.log('=== Router Guard Test Complete ===')
  })
})
