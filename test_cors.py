#!/usr/bin/env python3
"""
Test script to check CORS configuration
"""

import requests

def test_cors():
    """Test CORS preflight request"""
    
    url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    
    # Test preflight request (OPTIONS)
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "POST",
        "Access-Control-Request-Headers": "Content-Type"
    }
    
    try:
        print(f"Testing CORS preflight at: {url}")
        print(f"Origin: http://localhost:3000")
        
        response = requests.options(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers:")
        for key, value in response.headers.items():
            if 'access-control' in key.lower() or 'cors' in key.lower():
                print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("✅ CORS preflight successful!")
            return True
        else:
            print("❌ CORS preflight failed!")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_actual_request():
    """Test actual POST request with CORS headers"""
    
    url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    
    credentials = {
        "email": "<EMAIL>",
        "password": "Dick920815##"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:3000"
    }
    
    try:
        print(f"\nTesting actual POST request with CORS headers...")
        
        response = requests.post(url, json=credentials, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"CORS Response Headers:")
        for key, value in response.headers.items():
            if 'access-control' in key.lower():
                print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("✅ POST request with CORS successful!")
            return True
        else:
            print("❌ POST request failed!")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    print("=== CORS Testing ===")
    test_cors()
    test_actual_request()
