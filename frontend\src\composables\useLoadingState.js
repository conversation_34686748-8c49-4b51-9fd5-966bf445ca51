/**
 * Loading State Management Composable
 * Provides centralized loading state management with multiple loading indicators
 */

import { ref, computed, reactive } from 'vue'

/**
 * Create a loading state manager
 * @param {Object} options - Configuration options
 * @returns {Object} Loading state management functions and reactive state
 */
export function useLoadingState(options = {}) {
  const {
    initialState = false,
    timeout = 30000, // 30 seconds default timeout
    enableTimeout = true
  } = options

  // Main loading state
  const isLoading = ref(initialState)
  
  // Multiple loading states for different operations
  const loadingStates = reactive({})
  
  // Loading message
  const loadingMessage = ref('')
  
  // Timeout handling
  const timeoutId = ref(null)
  
  /**
   * Set main loading state
   * @param {boolean} loading - Loading state
   * @param {string} message - Optional loading message
   */
  const setLoading = (loading, message = '') => {
    isLoading.value = loading
    loadingMessage.value = message
    
    if (loading && enableTimeout) {
      // Clear existing timeout
      if (timeoutId.value) {
        clearTimeout(timeoutId.value)
      }
      
      // Set new timeout
      timeoutId.value = setTimeout(() => {
        console.warn('Loading operation timed out after', timeout, 'ms')
        isLoading.value = false
        loadingMessage.value = ''
      }, timeout)
    } else if (!loading && timeoutId.value) {
      clearTimeout(timeoutId.value)
      timeoutId.value = null
    }
  }

  /**
   * Set loading state for a specific operation
   * @param {string} key - Operation key
   * @param {boolean} loading - Loading state
   * @param {string} message - Optional loading message
   */
  const setOperationLoading = (key, loading, message = '') => {
    if (loading) {
      loadingStates[key] = {
        loading: true,
        message,
        startTime: Date.now()
      }
    } else {
      delete loadingStates[key]
    }
  }

  /**
   * Check if any operation is loading
   */
  const hasAnyLoading = computed(() => {
    return isLoading.value || Object.keys(loadingStates).length > 0
  })

  /**
   * Get all active loading operations
   */
  const activeOperations = computed(() => {
    return Object.keys(loadingStates)
  })

  /**
   * Get loading state for specific operation
   * @param {string} key - Operation key
   * @returns {boolean} Loading state
   */
  const isOperationLoading = (key) => {
    return !!loadingStates[key]?.loading
  }

  /**
   * Get loading message for specific operation
   * @param {string} key - Operation key
   * @returns {string} Loading message
   */
  const getOperationMessage = (key) => {
    return loadingStates[key]?.message || ''
  }

  /**
   * Wrapper for async operations with loading state
   * @param {Function} asyncFn - Async function to execute
   * @param {string} message - Loading message
   * @param {string} operationKey - Optional operation key for multiple loading states
   * @returns {Promise} Result of async function
   */
  const withLoading = async (asyncFn, message = 'Loading...', operationKey = null) => {
    try {
      if (operationKey) {
        setOperationLoading(operationKey, true, message)
      } else {
        setLoading(true, message)
      }

      const result = await asyncFn()
      return result
    } finally {
      if (operationKey) {
        setOperationLoading(operationKey, false)
      } else {
        setLoading(false)
      }
    }
  }

  /**
   * Clear all loading states
   */
  const clearAll = () => {
    isLoading.value = false
    loadingMessage.value = ''
    Object.keys(loadingStates).forEach(key => {
      delete loadingStates[key]
    })
    
    if (timeoutId.value) {
      clearTimeout(timeoutId.value)
      timeoutId.value = null
    }
  }

  /**
   * Get loading statistics
   */
  const getStats = () => {
    const operations = Object.entries(loadingStates).map(([key, state]) => ({
      key,
      message: state.message,
      duration: Date.now() - state.startTime
    }))

    return {
      mainLoading: isLoading.value,
      operationCount: operations.length,
      operations,
      hasTimeout: !!timeoutId.value
    }
  }

  return {
    // Reactive state
    isLoading,
    loadingMessage,
    loadingStates,
    hasAnyLoading,
    activeOperations,
    
    // Methods
    setLoading,
    setOperationLoading,
    isOperationLoading,
    getOperationMessage,
    withLoading,
    clearAll,
    getStats
  }
}

/**
 * Global loading state for application-wide loading indicators
 */
const globalLoadingState = useLoadingState({
  initialState: false,
  timeout: 60000, // 1 minute for global operations
  enableTimeout: true
})

export const useGlobalLoading = () => globalLoadingState

/**
 * Predefined loading states for common operations
 */
export const LOADING_OPERATIONS = {
  DASHBOARD_STATS: 'dashboard:stats',
  DASHBOARD_ACTIVITIES: 'dashboard:activities',
  USER_SEARCH: 'user:search',
  COMPETITION_LIST: 'competition:list',
  SCHOOL_LIST: 'school:list',
  QUALIFICATION_LIST: 'qualification:list',
  ROUTE_LIST: 'route:list',
  EXPORT_DATA: 'export:data',
  IMPORT_DATA: 'import:data',
  SAVE_SETTINGS: 'settings:save'
}

/**
 * Loading state hook for dashboard operations
 */
export function useDashboardLoading() {
  const loadingState = useLoadingState({
    timeout: 15000 // 15 seconds for dashboard operations
  })

  const loadDashboardData = async (loadFn) => {
    return await loadingState.withLoading(
      loadFn,
      'Loading dashboard data...',
      LOADING_OPERATIONS.DASHBOARD_STATS
    )
  }

  const loadActivities = async (loadFn) => {
    return await loadingState.withLoading(
      loadFn,
      'Loading recent activities...',
      LOADING_OPERATIONS.DASHBOARD_ACTIVITIES
    )
  }

  return {
    ...loadingState,
    loadDashboardData,
    loadActivities
  }
}
