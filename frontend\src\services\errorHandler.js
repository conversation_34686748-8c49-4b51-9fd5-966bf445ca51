/**
 * Global Error Handler
 * Centralized error handling for API calls and application errors
 */

import { ElMessage, ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'
import retryService, { withRetry, RETRY_CONFIGS } from './retryService.js'

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTH: 'AUTH_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  SERVER: 'SERVER_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  FORBIDDEN: 'FORBIDDEN',
  RATE_LIMIT: 'RATE_LIMIT',
  TIMEOUT: 'TIMEOUT_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

/**
 * Extract error information from standardized backend response
 * @param {Object} error - Error object
 * @returns {Object} Extracted error information
 */
export const extractErrorInfo = (error) => {
  // Handle standardized backend error response structure
  if (error.response?.data) {
    const data = error.response.data

    // New standardized format: { success: false, error: "ERROR_CODE", message: "...", data: {...}, code: 400 }
    if (data.success === false && data.error) {
      return {
        code: data.error,
        message: data.message || data.msg || 'An error occurred',
        details: data.data || null,
        status: data.code || error.response.status
      }
    }

    // Legacy format: { error: { code: "...", message: "...", details: {...} } }
    if (data.error && typeof data.error === 'object') {
      return {
        code: data.error.code || 'UNKNOWN_ERROR',
        message: data.error.message || 'An error occurred',
        details: data.error.details || null,
        status: error.response.status
      }
    }

    // Simple error message
    if (typeof data.error === 'string') {
      return {
        code: 'SIMPLE_ERROR',
        message: data.error,
        details: null,
        status: error.response.status
      }
    }
  }

  // Fallback for non-standardized errors
  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unexpected error occurred',
    details: null,
    status: error.status || error.response?.status || 0
  }
}

/**
 * Classify error based on status code and type
 * @param {Object} error - Error object
 * @returns {Object} Error classification
 */
export const classifyError = (error) => {
  const errorInfo = extractErrorInfo(error)
  const status = errorInfo.status

  let type = ERROR_TYPES.UNKNOWN
  let severity = ERROR_SEVERITY.MEDIUM
  let shouldRetry = false
  let shouldRedirect = false
  let redirectPath = null

  // First, try to classify by backend error code
  if (errorInfo.code) {
    switch (errorInfo.code) {
      case 'AUTHENTICATION_FAILED':
      case 'AUTH_ERROR':
        type = ERROR_TYPES.AUTH
        severity = ERROR_SEVERITY.HIGH
        shouldRedirect = true
        redirectPath = '/login'
        break

      case 'AUTHORIZATION_ERROR':
      case 'FORBIDDEN':
        type = ERROR_TYPES.FORBIDDEN
        severity = ERROR_SEVERITY.MEDIUM
        break

      case 'VALIDATION_ERROR':
      case 'INVALID_INPUT':
        type = ERROR_TYPES.VALIDATION
        severity = ERROR_SEVERITY.LOW
        break

      case 'NOT_FOUND':
      case 'RESOURCE_NOT_FOUND':
        type = ERROR_TYPES.NOT_FOUND
        severity = ERROR_SEVERITY.LOW
        break

      case 'RATE_LIMIT_EXCEEDED':
      case 'TOO_MANY_REQUESTS':
        type = ERROR_TYPES.RATE_LIMIT
        severity = ERROR_SEVERITY.MEDIUM
        shouldRetry = true
        break

      case 'NETWORK_ERROR':
      case 'CONNECTION_ERROR':
        type = ERROR_TYPES.NETWORK
        severity = ERROR_SEVERITY.HIGH
        shouldRetry = true
        break

      case 'TIMEOUT_ERROR':
      case 'REQUEST_TIMEOUT':
        type = ERROR_TYPES.TIMEOUT
        severity = ERROR_SEVERITY.MEDIUM
        shouldRetry = true
        break

      case 'SERVER_ERROR':
      case 'INTERNAL_SERVER_ERROR':
      case 'DATABASE_ERROR':
      case 'EXTERNAL_SERVICE_ERROR':
        type = ERROR_TYPES.SERVER
        severity = ERROR_SEVERITY.HIGH
        shouldRetry = true
        break
    }
  }

  // Classify by status code
  switch (status) {
    case 0:
      type = ERROR_TYPES.NETWORK
      severity = ERROR_SEVERITY.HIGH
      shouldRetry = true
      break
    
    case 400:
      type = ERROR_TYPES.VALIDATION
      severity = ERROR_SEVERITY.LOW
      break
    
    case 401:
      type = ERROR_TYPES.AUTH
      severity = ERROR_SEVERITY.HIGH
      shouldRedirect = true
      redirectPath = '/login'
      break
    
    case 403:
      type = ERROR_TYPES.FORBIDDEN
      severity = ERROR_SEVERITY.MEDIUM
      break
    
    case 404:
      type = ERROR_TYPES.NOT_FOUND
      severity = ERROR_SEVERITY.LOW
      break
    
    case 408:
      type = ERROR_TYPES.TIMEOUT
      severity = ERROR_SEVERITY.MEDIUM
      shouldRetry = true
      break
    
    case 429:
      type = ERROR_TYPES.RATE_LIMIT
      severity = ERROR_SEVERITY.MEDIUM
      shouldRetry = true
      break
    
    case 500:
    case 502:
    case 503:
    case 504:
      type = ERROR_TYPES.SERVER
      severity = ERROR_SEVERITY.HIGH
      shouldRetry = true
      break
    
    default:
      if (status >= 400 && status < 500) {
        type = ERROR_TYPES.VALIDATION
        severity = ERROR_SEVERITY.LOW
      } else if (status >= 500) {
        type = ERROR_TYPES.SERVER
        severity = ERROR_SEVERITY.HIGH
        shouldRetry = true
      }
  }

  return {
    type,
    severity,
    shouldRetry,
    shouldRedirect,
    redirectPath,
    status,
    errorInfo
  }
}

/**
 * Get user-friendly error message
 * @param {Object} error - Error object
 * @param {Object} classification - Error classification
 * @returns {string} User-friendly message
 */
export const getUserFriendlyMessage = (error, classification) => {
  const defaultMessages = {
    [ERROR_TYPES.NETWORK]: 'Network connection error. Please check your internet connection.',
    [ERROR_TYPES.AUTH]: 'Authentication failed. Please log in again.',
    [ERROR_TYPES.VALIDATION]: 'Invalid data provided. Please check your input.',
    [ERROR_TYPES.SERVER]: 'Server error occurred. Please try again later.',
    [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
    [ERROR_TYPES.FORBIDDEN]: 'You do not have permission to perform this action.',
    [ERROR_TYPES.RATE_LIMIT]: 'Too many requests. Please wait a moment and try again.',
    [ERROR_TYPES.TIMEOUT]: 'Request timed out. Please try again.',
    [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.'
  }

  // Use backend error message if available and meaningful
  const backendMessage = classification.errorInfo?.message
  if (backendMessage && backendMessage !== 'Network Error' && backendMessage !== 'An error occurred') {
    return backendMessage
  }

  // Fallback to custom error message
  const customMessage = error.error?.message || error.message
  if (customMessage && customMessage !== 'Network Error') {
    return customMessage
  }

  return defaultMessages[classification.type] || defaultMessages[ERROR_TYPES.UNKNOWN]
}

/**
 * Display error notification to user
 * @param {Object} error - Error object
 * @param {Object} options - Display options
 */
export const displayError = (error, options = {}) => {
  const classification = classifyError(error)
  const message = getUserFriendlyMessage(error, classification)
  
  const {
    showNotification = true,
    showMessage = false,
    duration = 4000,
    title = 'Error'
  } = options

  if (showNotification) {
    ElNotification({
      title,
      message,
      type: 'error',
      duration: classification.severity === ERROR_SEVERITY.CRITICAL ? 0 : duration,
      position: 'top-right'
    })
  }

  if (showMessage) {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose: true
    })
  }

  // Log error in development
  if (import.meta.env.DEV) {
    console.error('🚨 Error Handler:', {
      error,
      classification,
      message
    })
  }
}

/**
 * Handle authentication errors
 * @param {Object} error - Error object
 */
export const handleAuthError = (error) => {
  const router = useRouter()
  
  displayError(error, {
    title: 'Authentication Error',
    showNotification: true
  })

  // Clear auth data
  localStorage.removeItem('auth_token')
  localStorage.removeItem('user_data')
  
  // Redirect to login
  router.push('/login')
}

/**
 * Global error handler for unhandled errors
 * @param {Error} error - Error object
 * @param {Object} errorInfo - Additional error information
 */
export const globalErrorHandler = (error, errorInfo = {}) => {
  const classification = classifyError(error)
  
  // Handle critical errors
  if (classification.severity === ERROR_SEVERITY.CRITICAL) {
    displayError(error, {
      title: 'Critical Error',
      showNotification: true,
      duration: 0
    })
    
    // Log to external service in production
    if (import.meta.env.PROD) {
      // TODO: Integrate with error tracking service (Sentry, etc.)
      console.error('Critical error:', error, errorInfo)
    }
  } else {
    displayError(error)
  }

  // Handle redirects
  if (classification.shouldRedirect && classification.redirectPath) {
    const router = useRouter()
    router.push(classification.redirectPath)
  }
}

/**
 * Create error handler for specific contexts
 * @param {string} context - Error context (e.g., 'login', 'dashboard')
 * @param {Object} options - Handler options
 * @returns {Function} Error handler function
 */
export const createErrorHandler = (context, options = {}) => {
  return (error) => {
    const classification = classifyError(error)
    
    // Context-specific handling
    if (context === 'auth' && classification.type === ERROR_TYPES.AUTH) {
      handleAuthError(error)
      return
    }

    // Default handling
    displayError(error, {
      title: `${context} Error`,
      ...options
    })
  }
}

/**
 * Wrap async function with error handling and optional retry
 * @param {Function} asyncFn - Async function to wrap
 * @param {Object} options - Error handling options
 * @returns {Function} Wrapped function
 */
export const withErrorHandling = (asyncFn, options = {}) => {
  return async (...args) => {
    const {
      enableRetry = false,
      retryConfig = {},
      customHandler,
      context,
      rethrow = true
    } = options

    try {
      if (enableRetry) {
        // Use retry service for resilient execution
        return await withRetry(
          () => asyncFn(...args),
          retryConfig
        )
      } else {
        return await asyncFn(...args)
      }
    } catch (error) {
      const handler = customHandler || globalErrorHandler
      handler(error, { context })

      if (rethrow) {
        throw error
      }
    }
  }
}

/**
 * Create error-resilient API wrapper with retry logic
 * @param {Function} apiFn - API function to wrap
 * @param {Object} options - Configuration options
 * @returns {Function} Resilient API function
 */
export const createResilientApiWrapper = (apiFn, options = {}) => {
  const {
    retryConfig = RETRY_CONFIGS.NETWORK,
    errorContext = 'API',
    showUserError = true
  } = options

  return async (...args) => {
    try {
      return await withRetry(
        () => apiFn(...args),
        retryConfig
      )
    } catch (error) {
      const classification = classifyError(error)

      if (showUserError) {
        displayError(error, {
          title: `${errorContext} Error`,
          showNotification: classification.severity !== ERROR_SEVERITY.LOW
        })
      }

      // Log error details
      console.error(`🚨 ${errorContext} Error:`, {
        error: classification.errorInfo,
        classification,
        args
      })

      throw error
    }
  }
}
