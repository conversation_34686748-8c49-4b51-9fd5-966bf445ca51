/**
 * Retry Service
 * Provides intelligent retry mechanisms for failed API requests
 */

import { ERROR_TYPES, classifyError } from './errorHandler.js'

class RetryService {
  constructor() {
    this.retryAttempts = new Map()
    this.defaultConfig = {
      maxRetries: 3,
      baseDelay: 1000, // 1 second
      maxDelay: 30000, // 30 seconds
      backoffMultiplier: 2,
      jitter: true,
      retryCondition: this.defaultRetryCondition.bind(this)
    }
    
    console.log('🔄 Retry Service initialized')
  }

  /**
   * Default retry condition - determines if an error should be retried
   * @param {Object} error - Error object
   * @param {number} attemptNumber - Current attempt number
   * @returns {boolean} Whether to retry
   */
  defaultRetryCondition(error, attemptNumber) {
    const classification = classifyError(error)
    
    // Don't retry certain error types
    const nonRetryableTypes = [
      ERROR_TYPES.AUTH,
      ERROR_TYPES.FORBIDDEN,
      ERROR_TYPES.VALIDATION,
      ERROR_TYPES.NOT_FOUND
    ]
    
    if (nonRetryableTypes.includes(classification.type)) {
      return false
    }
    
    // Retry network errors, timeouts, and server errors
    const retryableTypes = [
      ERROR_TYPES.NETWORK,
      ERROR_TYPES.TIMEOUT,
      ERROR_TYPES.SERVER,
      ERROR_TYPES.RATE_LIMIT
    ]
    
    return retryableTypes.includes(classification.type)
  }

  /**
   * Calculate delay for next retry attempt
   * @param {number} attemptNumber - Current attempt number (0-based)
   * @param {Object} config - Retry configuration
   * @returns {number} Delay in milliseconds
   */
  calculateDelay(attemptNumber, config) {
    const { baseDelay, maxDelay, backoffMultiplier, jitter } = config
    
    // Exponential backoff
    let delay = baseDelay * Math.pow(backoffMultiplier, attemptNumber)
    
    // Add jitter to prevent thundering herd
    if (jitter) {
      delay = delay * (0.5 + Math.random() * 0.5)
    }
    
    // Cap at maximum delay
    return Math.min(delay, maxDelay)
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Generate retry key for tracking attempts
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {string} Retry key
   */
  generateRetryKey(url, options = {}) {
    const { method = 'GET', params = {} } = options
    const keyData = {
      url,
      method: method.toUpperCase(),
      params: JSON.stringify(params, Object.keys(params).sort())
    }
    
    return btoa(JSON.stringify(keyData))
  }

  /**
   * Execute function with retry logic
   * @param {Function} asyncFn - Async function to execute
   * @param {Object} config - Retry configuration
   * @param {string} retryKey - Optional retry key for tracking
   * @returns {Promise} Result of successful execution
   */
  async withRetry(asyncFn, config = {}, retryKey = null) {
    const finalConfig = { ...this.defaultConfig, ...config }
    const { maxRetries, retryCondition } = finalConfig
    
    // Generate retry key if not provided
    if (!retryKey) {
      retryKey = `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
    
    let lastError = null
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Track attempt
        this.retryAttempts.set(retryKey, {
          attempt,
          maxRetries,
          startTime: Date.now()
        })
        
        const result = await asyncFn()
        
        // Success - clean up tracking
        this.retryAttempts.delete(retryKey)
        
        if (attempt > 0) {
          console.log(`✅ Retry succeeded on attempt ${attempt + 1} for key: ${retryKey}`)
        }
        
        return result
      } catch (error) {
        lastError = error
        
        // Check if we should retry
        if (attempt < maxRetries && retryCondition(error, attempt)) {
          const delay = this.calculateDelay(attempt, finalConfig)
          
          console.log(`🔄 Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms for key: ${retryKey}`, {
            error: error.message,
            status: error.status || error.response?.status
          })
          
          await this.sleep(delay)
          continue
        }
        
        // No more retries or shouldn't retry
        this.retryAttempts.delete(retryKey)
        
        if (attempt > 0) {
          console.error(`❌ All retry attempts failed for key: ${retryKey}`, {
            attempts: attempt + 1,
            finalError: error.message
          })
        }
        
        throw error
      }
    }
    
    throw lastError
  }

  /**
   * Retry wrapper for API requests
   * @param {string} url - Request URL
   * @param {Function} requestFn - Function that makes the request
   * @param {Object} options - Request and retry options
   * @returns {Promise} Request result
   */
  async retryRequest(url, requestFn, options = {}) {
    const { retryConfig = {}, requestOptions = {} } = options
    const retryKey = this.generateRetryKey(url, requestOptions)
    
    return this.withRetry(requestFn, retryConfig, retryKey)
  }

  /**
   * Create a retry-enabled version of an async function
   * @param {Function} asyncFn - Async function to wrap
   * @param {Object} config - Retry configuration
   * @returns {Function} Retry-enabled function
   */
  createRetryWrapper(asyncFn, config = {}) {
    return async (...args) => {
      return this.withRetry(() => asyncFn(...args), config)
    }
  }

  /**
   * Get retry statistics
   * @returns {Object} Retry statistics
   */
  getStats() {
    const activeRetries = Array.from(this.retryAttempts.entries()).map(([key, data]) => ({
      key,
      attempt: data.attempt,
      maxRetries: data.maxRetries,
      duration: Date.now() - data.startTime
    }))
    
    return {
      activeRetries: activeRetries.length,
      retries: activeRetries
    }
  }

  /**
   * Clear all retry tracking
   */
  clear() {
    this.retryAttempts.clear()
    console.log('🧹 Retry service cleared')
  }

  /**
   * Create retry configuration for specific error types
   * @param {string} errorType - Error type to configure for
   * @returns {Object} Retry configuration
   */
  static createConfigForErrorType(errorType) {
    const configs = {
      [ERROR_TYPES.NETWORK]: {
        maxRetries: 3,
        baseDelay: 1000,
        backoffMultiplier: 2
      },
      [ERROR_TYPES.TIMEOUT]: {
        maxRetries: 2,
        baseDelay: 2000,
        backoffMultiplier: 1.5
      },
      [ERROR_TYPES.SERVER]: {
        maxRetries: 3,
        baseDelay: 2000,
        backoffMultiplier: 2
      },
      [ERROR_TYPES.RATE_LIMIT]: {
        maxRetries: 5,
        baseDelay: 5000,
        backoffMultiplier: 2,
        maxDelay: 60000 // 1 minute max for rate limits
      }
    }
    
    return configs[errorType] || {}
  }
}

// Create singleton instance
const retryService = new RetryService()

export default retryService

// Export helper functions
export const withRetry = (asyncFn, config, retryKey) => 
  retryService.withRetry(asyncFn, config, retryKey)

export const retryRequest = (url, requestFn, options) => 
  retryService.retryRequest(url, requestFn, options)

export const createRetryWrapper = (asyncFn, config) => 
  retryService.createRetryWrapper(asyncFn, config)

// Export retry configurations for common scenarios
export const RETRY_CONFIGS = {
  NETWORK: RetryService.createConfigForErrorType(ERROR_TYPES.NETWORK),
  TIMEOUT: RetryService.createConfigForErrorType(ERROR_TYPES.TIMEOUT),
  SERVER: RetryService.createConfigForErrorType(ERROR_TYPES.SERVER),
  RATE_LIMIT: RetryService.createConfigForErrorType(ERROR_TYPES.RATE_LIMIT),
  AGGRESSIVE: {
    maxRetries: 5,
    baseDelay: 500,
    backoffMultiplier: 1.5
  },
  CONSERVATIVE: {
    maxRetries: 2,
    baseDelay: 2000,
    backoffMultiplier: 2
  }
}
