/**
 * School/University Service
 * Integrates with Community API for university and major data
 * Replaces mock schoolService with real API calls
 */

import { communityApi } from './api/communityApi.js'
import { analyticsApi } from './api/analyticsApi.js'
import { universityAdapter } from './adapters/universityAdapter.js'
import { analyticsAdapter } from './adapters/analyticsAdapter.js'
import { handleApiError, logApiCall, isMockApiEnabled } from '../utils/apiHelpers.js'

// Import mock service as fallback
import { schoolService as mockSchoolService } from './mock/schoolService.js'

/**
 * Real School Service Implementation
 * Maps university data from Community API to school interface
 */
class SchoolService {
  /**
   * Get schools/universities list
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.country - Filter by country
   * @param {string} params.province - Filter by province
   * @param {string} params.search - Search term
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort order (asc/desc)
   * @returns {Promise<Object>} Schools list response
   */
  async getSchools(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/schools (mock)', params, null)
      return mockSchoolService.getSchools(params)
    }

    try {
      logApiCall('GET', '/community/universities', params, null)

      // Transform parameters for Community API
      const apiParams = {
        limit: params.limit || 20,
        offset: params.page ? (params.page - 1) * (params.limit || 20) : 0,
        country: params.country,
        province: params.province
      }

      // Get universities from Community API
      let universitiesResponse
      if (params.search) {
        // Use search function for text search
        universitiesResponse = await communityApi.searchUniversities(params.search, apiParams)
      } else {
        // Use regular get function
        universitiesResponse = await communityApi.getUniversities(apiParams)
      }

      if (!universitiesResponse.success) {
        throw new Error(universitiesResponse.error?.message || 'Failed to fetch universities')
      }

      // Transform universities to school format
      const transformedData = universityAdapter.transform(universitiesResponse, {
        type: 'list',
        isPaginated: true
      })

      // Apply additional filtering and sorting
      let schools = transformedData.data
      
      // Apply client-side filters that aren't supported by API
      if (params.type) {
        schools = schools.filter(school => school.type === params.type)
      }
      
      if (params.level) {
        schools = schools.filter(school => school.level === params.level)
      }
      
      if (params.status) {
        schools = schools.filter(school => school.status === params.status)
      }

      // Apply sorting
      if (params.sortBy) {
        schools = this.sortSchools(schools, params.sortBy, params.sortOrder)
      }

      // Enhance with statistics if available
      const enhancedSchools = await this.enhanceSchoolsWithStats(schools)

      const result = {
        success: true,
        data: enhancedSchools,
        pagination: {
          page: transformedData.page,
          limit: transformedData.pageSize,
          total: transformedData.total,
          pages: Math.ceil(transformedData.total / transformedData.pageSize),
          hasNext: transformedData.hasMore,
          hasPrev: transformedData.page > 1
        }
      }

      logApiCall('GET', '/community/universities', params, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/community/universities', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get school/university by ID
   * @param {string} id - University ID
   * @returns {Promise<Object>} School details response
   */
  async getSchool(id) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', `/schools/${id} (mock)`, null, null)
      return mockSchoolService.getSchool(id)
    }

    try {
      logApiCall('GET', `/community/universities/${id}`, null, null)

      // Get all universities and find by ID (Community API doesn't have single university endpoint)
      const universitiesResponse = await communityApi.getAllUniversities()

      if (!universitiesResponse.success) {
        throw new Error(universitiesResponse.error?.message || 'Failed to fetch university')
      }

      const university = universitiesResponse.data.find(u => 
        u.university_id === id || u.id === id
      )

      if (!university) {
        return {
          success: false,
          error: {
            code: 'SCHOOL_NOT_FOUND',
            message: 'School not found'
          }
        }
      }

      // Transform to detailed school format
      const transformedSchool = universityAdapter.transformItem(university, 'detail')

      // Enhance with statistics
      const schoolWithStats = await this.enhanceSchoolWithStats(transformedSchool)

      const result = {
        success: true,
        data: schoolWithStats
      }

      logApiCall('GET', `/community/universities/${id}`, null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', `/community/universities/${id}`, null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get school statistics
   * @returns {Promise<Object>} School statistics response
   */
  async getSchoolStats() {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/schools/stats (mock)', null, null)
      return mockSchoolService.getSchoolStats()
    }

    try {
      logApiCall('GET', '/schools/stats', null, null)

      // Try to use dedicated school statistics endpoint first
      try {
        // For overall school statistics, we'll aggregate from individual school stats
        const universitiesResponse = await communityApi.getAllUniversities()

        if (!universitiesResponse.success) {
          throw new Error('Failed to fetch universities data')
        }

        // Get statistics for top schools using new endpoint
        const schoolRankingsResponse = await analyticsApi.getSchoolRankings({ page_size: 10, current_page: 1 })

        let schoolStatsData = []
        if (schoolRankingsResponse.success && schoolRankingsResponse.data) {
          // Get detailed stats for top schools
          const topSchools = schoolRankingsResponse.data.slice(0, 5) // Get top 5 schools
          const schoolStatsPromises = topSchools.map(school =>
            analyticsApi.getSchoolStats(school.university || school.school_name)
              .catch(err => {
                console.warn(`Failed to get stats for school ${school.university}:`, err)
                return { success: false, data: null }
              })
          )

          const schoolStatsResults = await Promise.allSettled(schoolStatsPromises)
          schoolStatsData = schoolStatsResults
            .filter(result => result.status === 'fulfilled' && result.value.success)
            .map(result => result.value.data)
        }

        // Fallback to summary statistics if needed
        const summaryStatsResponse = await analyticsApi.getSummaryStatistics()

        if (!universitiesResponse.success) {
          throw new Error('Failed to fetch universities data')
        }

      // Transform analytics data
      const summaryStats = analyticsAdapter.transformSummaryStatistics(summaryStatsResponse.data || [])
      const schoolRankings = analyticsAdapter.transformSchoolRankings(schoolRankingsResponse)

      // Calculate statistics from universities data
      const universities = universitiesResponse.data
      const totalSchools = universities.length

      // Geographic distribution
      const geographicDistribution = universities.reduce((acc, u) => {
        const country = u.country || 'Unknown'
        acc[country] = (acc[country] || 0) + 1
        return acc
      }, {})

      // Province distribution
      const provinceDistribution = universities.reduce((acc, u) => {
        const province = u.province || 'Unknown'
        acc[province] = (acc[province] || 0) + 1
        return acc
      }, {})

      // Top performing schools from rankings
      const topPerformingSchools = schoolRankings.schools.map((school, index) => ({
        school: {
          id: school.university || school.universityName,
          name: school.university || school.universityName,
          displayName: school.university || school.universityName
        },
        rank: school.rank || index + 1,
        score: school.totalCredits || 0,
        metrics: {
          totalCredits: school.totalCredits || 0,
          displayCredits: school.displayCredits || '0 credits'
        }
      }))

      const stats = {
        totalSchools,
        totalStudents: summaryStats.totalParticipants || 0,
        averageParticipationRate: 0, // Not available from current APIs
        topPerformingSchools,
        geographicDistribution,
        provinceDistribution,
        typeDistribution: {
          'University': totalSchools // Simplified - all are universities
        },
        levelDistribution: {
          'Higher Education': totalSchools // Simplified
        },
        summary: summaryStats
      }

      const result = {
        success: true,
        data: stats
      }

      logApiCall('GET', '/schools/stats', null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/schools/stats', null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Create school (not supported by Community API)
   * @param {Object} schoolData - School data
   * @returns {Promise<Object>} Create response
   */
  async createSchool(schoolData) {
    // Always use mock for create operations
    logApiCall('POST', '/schools (mock)', schoolData, null)
    return mockSchoolService.createSchool(schoolData)
  }

  /**
   * Sort schools array
   * @param {Array} schools - Schools array
   * @param {string} sortBy - Sort field
   * @param {string} sortOrder - Sort order
   * @returns {Array} Sorted schools
   */
  sortSchools(schools, sortBy, sortOrder = 'asc') {
    return schools.sort((a, b) => {
      let aVal = a[sortBy]
      let bVal = b[sortBy]

      // Handle nested properties
      if (sortBy === 'participationRate' || sortBy === 'averageScore') {
        aVal = a.stats?.[sortBy] || 0
        bVal = b.stats?.[sortBy] || 0
      }

      // Handle string comparison
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }

      // Sort logic
      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }

  /**
   * Enhance schools with statistics
   * @param {Array} schools - Schools array
   * @returns {Promise<Array>} Enhanced schools
   */
  async enhanceSchoolsWithStats(schools) {
    // For now, return schools as-is
    // In the future, could fetch individual stats for each school
    return schools.map(school => ({
      ...school,
      stats: school.stats || {
        totalStudents: 0,
        activeStudents: 0,
        totalCompetitions: 0,
        totalSubmissions: 0,
        totalCreditsEarned: 0,
        averageScore: 0,
        participationRate: 0,
        completionRate: 0,
        winRate: 0
      }
    }))
  }

  /**
   * Enhance single school with statistics
   * @param {Object} school - School object
   * @returns {Promise<Object>} Enhanced school
   */
  async enhanceSchoolWithStats(school) {
    try {
      // Use new dedicated analytics endpoint for school-specific stats
      const stats = await analyticsApi.getSchoolStats(school.university || school.name || school.id)

      return {
        ...school,
        stats: stats.success ? stats.data : school.stats || {}
      }
    } catch (error) {
      console.warn(`Failed to enhance school ${school.id || school.name} with stats:`, error)
      // Return school with default stats if enhancement fails
      return {
        ...school,
        stats: school.stats || {
          totalStudents: 0,
          activeStudents: 0,
          totalCompetitions: 0,
          totalSubmissions: 0,
          totalCreditsEarned: 0,
          averageScore: 0,
          participationRate: 0,
          completionRate: 0,
          winRate: 0
        }
      }
    }
  }
}

// Create and export service instance
export const schoolService = new SchoolService()

// Export individual methods for convenience
export const {
  getSchools,
  getSchool,
  getSchoolStats,
  createSchool
} = schoolService
