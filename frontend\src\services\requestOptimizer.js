/**
 * Request Optimizer Service
 * Optimizes API call patterns to reduce redundant requests through deduplication,
 * batching, and intelligent request scheduling
 */

import cacheService from './cacheService.js'

class RequestOptimizer {
  constructor() {
    this.pendingRequests = new Map()
    this.requestQueue = []
    this.batchTimeout = null
    this.batchDelay = 50 // 50ms batch delay
    this.maxBatchSize = 10
    
    console.log('🚀 Request Optimizer initialized')
  }

  /**
   * Generate request key for deduplication
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {string} Request key
   */
  generateRequestKey(url, options = {}) {
    const { method = 'GET', params = {}, data = {} } = options
    const keyData = {
      url,
      method: method.toUpperCase(),
      params: JSON.stringify(params, Object.keys(params).sort()),
      data: JSON.stringify(data, Object.keys(data).sort())
    }
    
    return btoa(JSON.stringify(keyData))
  }

  /**
   * Deduplicate identical requests
   * @param {string} requestKey - Request key
   * @param {Function} requestFn - Function that makes the actual request
   * @returns {Promise} Request promise
   */
  async deduplicateRequest(requestKey, requestFn) {
    // Check if request is already pending
    if (this.pendingRequests.has(requestKey)) {
      console.log(`🔄 Deduplicating request: ${requestKey.substring(0, 20)}...`)
      return this.pendingRequests.get(requestKey)
    }

    // Create new request promise
    const requestPromise = requestFn()
      .finally(() => {
        // Remove from pending requests when completed
        this.pendingRequests.delete(requestKey)
      })

    // Store pending request
    this.pendingRequests.set(requestKey, requestPromise)
    
    return requestPromise
  }

  /**
   * Optimized request wrapper with caching and deduplication
   * @param {string} url - Request URL
   * @param {Function} requestFn - Function that makes the actual request
   * @param {Object} options - Options for caching and optimization
   * @returns {Promise} Request result
   */
  async optimizedRequest(url, requestFn, options = {}) {
    const {
      cache = true,
      cacheTTL = 300000, // 5 minutes default
      deduplicate = true,
      requestOptions = {}
    } = options

    const requestKey = this.generateRequestKey(url, requestOptions)
    
    // Try cache first if enabled
    if (cache) {
      const cached = cacheService.get(requestKey)
      if (cached !== null) {
        console.log(`💾 Cache hit for: ${url}`)
        return cached
      }
    }

    // Deduplicate if enabled
    if (deduplicate) {
      return this.deduplicateRequest(requestKey, async () => {
        const result = await requestFn()
        
        // Cache result if successful and caching is enabled
        if (cache && result) {
          cacheService.set(requestKey, result, cacheTTL)
        }
        
        return result
      })
    }

    // Execute request directly
    const result = await requestFn()
    
    // Cache result if successful and caching is enabled
    if (cache && result) {
      cacheService.set(requestKey, result, cacheTTL)
    }
    
    return result
  }

  /**
   * Batch multiple requests together
   * @param {Array} requests - Array of request configurations
   * @returns {Promise<Array>} Array of results
   */
  async batchRequests(requests) {
    if (requests.length === 0) return []
    
    console.log(`📦 Batching ${requests.length} requests`)
    
    // Execute all requests in parallel
    const results = await Promise.allSettled(
      requests.map(({ url, requestFn, options = {} }) =>
        this.optimizedRequest(url, requestFn, options)
      )
    )
    
    // Transform results
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        console.error(`Batch request ${index} failed:`, result.reason)
        return null
      }
    })
  }

  /**
   * Add request to batch queue
   * @param {Object} requestConfig - Request configuration
   * @returns {Promise} Promise that resolves when batch is processed
   */
  queueRequest(requestConfig) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        ...requestConfig,
        resolve,
        reject
      })
      
      this.scheduleBatch()
    })
  }

  /**
   * Schedule batch processing
   */
  scheduleBatch() {
    if (this.batchTimeout) return
    
    this.batchTimeout = setTimeout(() => {
      this.processBatch()
    }, this.batchDelay)
  }

  /**
   * Process queued requests in batch
   */
  async processBatch() {
    if (this.requestQueue.length === 0) {
      this.batchTimeout = null
      return
    }
    
    const batch = this.requestQueue.splice(0, this.maxBatchSize)
    this.batchTimeout = null
    
    try {
      const results = await this.batchRequests(
        batch.map(({ url, requestFn, options }) => ({ url, requestFn, options }))
      )
      
      // Resolve individual promises
      batch.forEach((request, index) => {
        const result = results[index]
        if (result !== null) {
          request.resolve(result)
        } else {
          request.reject(new Error('Batch request failed'))
        }
      })
    } catch (error) {
      // Reject all promises in batch
      batch.forEach(request => {
        request.reject(error)
      })
    }
    
    // Process remaining queue if any
    if (this.requestQueue.length > 0) {
      this.scheduleBatch()
    }
  }

  /**
   * Prefetch data for anticipated requests
   * @param {Array} urls - URLs to prefetch
   * @param {Object} options - Prefetch options
   */
  async prefetch(urls, options = {}) {
    const {
      priority = 'low',
      cacheTTL = 600000 // 10 minutes for prefetched data
    } = options
    
    console.log(`🔮 Prefetching ${urls.length} resources`)
    
    // Use requestIdleCallback if available for low priority prefetching
    const executePrefetch = () => {
      urls.forEach(({ url, requestFn }) => {
        this.optimizedRequest(url, requestFn, {
          cache: true,
          cacheTTL,
          deduplicate: true
        }).catch(error => {
          console.warn(`Prefetch failed for ${url}:`, error)
        })
      })
    }
    
    if (priority === 'low' && window.requestIdleCallback) {
      window.requestIdleCallback(executePrefetch)
    } else {
      // Execute immediately for high priority or if requestIdleCallback not available
      setTimeout(executePrefetch, 0)
    }
  }

  /**
   * Clear all pending requests and cache
   */
  clear() {
    this.pendingRequests.clear()
    this.requestQueue.length = 0
    
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout)
      this.batchTimeout = null
    }
    
    console.log('🧹 Request optimizer cleared')
  }

  /**
   * Get optimizer statistics
   * @returns {Object} Statistics
   */
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      queuedRequests: this.requestQueue.length,
      batchScheduled: !!this.batchTimeout,
      cacheStats: cacheService.getStats()
    }
  }
}

// Create singleton instance
const requestOptimizer = new RequestOptimizer()

export default requestOptimizer

// Export helper functions for common use cases
export const optimizedFetch = (url, requestFn, options) => 
  requestOptimizer.optimizedRequest(url, requestFn, options)

export const batchFetch = (requests) => 
  requestOptimizer.batchRequests(requests)

export const queueFetch = (requestConfig) => 
  requestOptimizer.queueRequest(requestConfig)

export const prefetchData = (urls, options) => 
  requestOptimizer.prefetch(urls, options)
