/**
 * Competition Service
 * Integrates with Camp Admin API and Analytics API for competition data
 * Replaces mock competitionService with real API calls
 */

import { competitionsApi } from './api/competitionsApi.js'
import { campAdminApi } from './api/campAdminApi.js'
import { analyticsApi } from './api/analyticsApi.js'
import { competitionAdapter } from './adapters/competitionAdapter.js'
import { analyticsAdapter } from './adapters/analyticsAdapter.js'
import { handleApiError, logApiCall, isMockApiEnabled } from '../utils/apiHelpers.js'

// Import mock service as fallback
import { competitionService as mockCompetitionService } from './mock/competitionService.js'

// Helper function to extract statistics values
const extractStatValue = (statistics, statName) => {
  if (!Array.isArray(statistics)) return 0
  const stat = statistics.find(s => s.statistics_name === statName)
  return stat ? (stat.display_value || stat.statistics_value || 0) : 0
}

/**
 * Real Competition Service Implementation
 * Maps competition data from Camp Admin API to competition interface
 */
class CompetitionService {
  /**
   * Get competitions list
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.status - Filter by status
   * @param {string} params.type - Filter by type
   * @param {string} params.category - Filter by category
   * @param {string} params.difficulty - Filter by difficulty
   * @param {string} params.search - Search term
   * @param {string} params.routeId - Filter by route ID
   * @param {boolean} params.featured - Filter featured competitions
   * @param {string} params.sortBy - Sort field
   * @param {string} params.sortOrder - Sort order (asc/desc)
   * @returns {Promise<Object>} Competitions list response
   */
  async getCompetitions(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/competitions (mock)', params, null)
      return mockCompetitionService.getCompetitions(params)
    }

    try {
      logApiCall('GET', '/camp-admin/competitions', params, null)

      // Transform parameters for Camp Admin API with server-side search support
      const apiParams = {
        limit: params.limit || 20,
        offset: params.page ? (params.page - 1) * (params.limit || 20) : 0,
        route_id: params.routeId,
        status: params.status
      }

      // Add server-side search parameters if search term is provided
      if (params.search && params.search.trim()) {
        apiParams.q = params.search.trim()
        apiParams.search_fields = 'competition_name,route_name'

        // Add sorting for search results
        if (params.sortBy) {
          const sortFieldMap = {
            'title': 'competition_name',
            'name': 'competition_name',
            'startDate': 'start_date',
            'status': 'status'
          }
          apiParams.sort_by = sortFieldMap[params.sortBy] || params.sortBy
          apiParams.sort_order = params.sortOrder === 'desc' ? 'DESC' : 'ASC'
        } else {
          // Default sort for search results
          apiParams.sort_by = 'start_date'
          apiParams.sort_order = 'DESC'
        }
      }

      // Get competitions from Camp Admin API
      const competitionsResponse = await competitionsApi.getCompetitions(apiParams)

      if (!competitionsResponse.success) {
        throw new Error(competitionsResponse.error?.message || 'Failed to fetch competitions')
      }

      // Transform competitions to frontend format
      const transformedData = competitionAdapter.transform(competitionsResponse, {
        type: 'list',
        isPaginated: false // Camp Admin API doesn't return pagination info
      })

      // Apply additional client-side filtering
      let competitions = transformedData.data

      // Apply filters not supported by API
      if (params.type) {
        competitions = competitions.filter(comp => comp.type === params.type)
      }

      if (params.category) {
        competitions = competitions.filter(comp => comp.category === params.category)
      }

      if (params.difficulty) {
        competitions = competitions.filter(comp => comp.difficulty === params.difficulty)
      }

      // Only apply client-side search if server-side search was not used
      if (params.search && !apiParams.q) {
        const searchLower = params.search.toLowerCase()
        competitions = competitions.filter(comp =>
          comp.title.toLowerCase().includes(searchLower) ||
          comp.name.toLowerCase().includes(searchLower) ||
          (comp.description && comp.description.toLowerCase().includes(searchLower))
        )
      }

      if (params.featured !== undefined) {
        competitions = competitions.filter(comp => comp.featured === params.featured)
      }

      // Apply client-side sorting only if server-side sorting was not used
      if (params.sortBy && !apiParams.sort_by) {
        competitions = this.sortCompetitions(competitions, params.sortBy, params.sortOrder)
      }

      // Enhance with statistics if available
      const enhancedCompetitions = await this.enhanceCompetitionsWithStats(competitions)

      // Create pagination info (client-side)
      const total = enhancedCompetitions.length
      const page = params.page || 1
      const limit = params.limit || 20
      const offset = (page - 1) * limit
      const paginatedData = enhancedCompetitions.slice(offset, offset + limit)

      const result = {
        success: true,
        data: paginatedData,
        pagination: {
          total: total,
          limit: parseInt(limit),
          offset: offset,
          page: parseInt(page),
          total_pages: Math.ceil(total / limit),
          has_next: offset + limit < total,
          has_prev: page > 1
        }
      }

      logApiCall('GET', '/camp-admin/competitions', params, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/competitions', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition by ID
   * @param {string} id - Competition ID
   * @returns {Promise<Object>} Competition details response
   */
  async getCompetition(id) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', `/competitions/${id} (mock)`, null, null)
      return mockCompetitionService.getCompetition(id)
    }

    try {
      logApiCall('GET', `/camp-admin/competitions/${id}`, null, null)

      // Get all competitions and find by ID (Camp Admin API doesn't have single competition endpoint)
      const competitionsResponse = await competitionsApi.getCompetitions({ limit: 1000, offset:0 })

      if (!competitionsResponse.success) {
        throw new Error(competitionsResponse.error?.message || 'Failed to fetch competition')
      }

      const competition = competitionsResponse.data.find(c => 
        c.id === id || c.competition_id === id
      )

      if (!competition) {
        return {
          success: false,
          error: {
            code: 'COMPETITION_NOT_FOUND',
            message: 'Competition not found'
          }
        }
      }

      // Transform to detailed competition format
      const transformedCompetition = competitionAdapter.transformItem(competition, 'detail')

      // Enhance with statistics and qualifications
      const competitionWithStats = await this.enhanceCompetitionWithStats(transformedCompetition)
      const competitionWithQualifications = await this.enhanceCompetitionWithQualifications(competitionWithStats)

      const result = {
        success: true,
        data: competitionWithQualifications
      }

      logApiCall('GET', `/camp-admin/competitions/${id}`, null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', `/camp-admin/competitions/${id}`, null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition statistics
   * @returns {Promise<Object>} Competition statistics response
   */
  async getCompetitionStats() {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/competitions/stats (mock)', null, null)
      return mockCompetitionService.getCompetitionStats()
    }

    try {
      logApiCall('GET', '/competitions/stats', null, null)

      // Try to use dedicated competition statistics endpoints first
      try {
        // Get overall competition statistics using new dashboard endpoint
        const dashboardResponse = await analyticsApi.getDashboard()

        if (dashboardResponse.success && dashboardResponse.data) {
          // Extract competition statistics from dashboard data
          const dashboardData = dashboardResponse.data

          // Get additional route information
          const routesResponse = await competitionsApi.getRoutes({ limit: 100, offset: 0 })

          const result = {
            success: true,
            data: {
              total_competitions: extractStatValue(dashboardData.summary_statistics, 'total_competitions'),
              active_competitions: extractStatValue(dashboardData.summary_statistics, 'active_competitions'),
              total_participants: extractStatValue(dashboardData.summary_statistics, 'total_participants'),
              total_submissions: extractStatValue(dashboardData.summary_statistics, 'total_submissions'),
              routes: routesResponse.success ? routesResponse.data : [],
              recent_competitions: dashboardData.recent_competitions || [],
              route_rankings: dashboardData.route_rankings || []
            }
          }

          logApiCall('GET', '/competitions/stats', null, result)
          return result
        }
      } catch (dashboardError) {
        console.warn('New dashboard endpoint failed, falling back to legacy method:', dashboardError)
      }

      // Fallback to legacy method if new endpoint fails
      const [dashboardResponse, summaryStatsResponse, legacyRoutesResponse] = await Promise.all([
        competitionsApi.getCompetitionsDashboardData(),
        analyticsApi.getSummaryStatistics(),
        competitionsApi.getRoutes({ limit: 100, offset: 0 })
      ])

      if (!dashboardResponse.success) {
        throw new Error('Failed to fetch dashboard data')
      }

      // Transform analytics data
      const summaryStats = analyticsAdapter.transformSummaryStatistics(summaryStatsResponse.data || [])
      const competitions = dashboardResponse.data.recent_competitions || []
      const routes = dashboardResponse.data.available_routes || (legacyRoutesResponse.success ? legacyRoutesResponse.data : [])

      // Calculate statistics
      const totalCompetitions = dashboardResponse.data.total_competitions || competitions.length
      const activeCompetitions = competitions.filter(c => c.status === 'active').length
      const upcomingCompetitions = competitions.filter(c => c.status === 'upcoming').length
      const completedCompetitions = competitions.filter(c => c.status === 'completed').length

      // Route distribution
      const routeDistribution = routes.reduce((acc, route) => {
        acc[route.name] = route.competition_count || 0
        return acc
      }, {})

      // Status distribution
      const statusDistribution = competitions.reduce((acc, comp) => {
        const status = comp.status || 'unknown'
        acc[status] = (acc[status] || 0) + 1
        return acc
      }, {})

      const stats = {
        totalCompetitions,
        activeCompetitions,
        upcomingCompetitions,
        completedCompetitions,
        totalParticipants: summaryStats.totalParticipants || 0,
        totalSubmissions: summaryStats.totalSubmissions || 0,
        averageParticipants: totalCompetitions > 0 ? Math.round((summaryStats.totalParticipants || 0) / totalCompetitions) : 0,
        routeDistribution,
        statusDistribution,
        recentCompetitions: competitions.slice(0, 10),
        availableRoutes: routes,
        summary: summaryStats
      }

      const result = {
        success: true,
        data: stats
      }

      logApiCall('GET', '/competitions/stats', null, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/competitions/stats', null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get competition routes
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Routes list response
   */
  async getRoutes(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/routes (mock)', params, null)
      return mockCompetitionService.getRoutes(params)
    }

    try {
      logApiCall('GET', '/camp-admin/routes', params, null)

      const routesResponse = await competitionsApi.getRoutes(params)

      if (!routesResponse.success) {
        throw new Error(routesResponse.error?.message || 'Failed to fetch routes')
      }

      // Transform routes
      const transformedData = competitionAdapter.transformRoutes(routesResponse, {
        type: 'list'
      })

      logApiCall('GET', '/camp-admin/routes', params, transformedData)
      return transformedData

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/routes', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Create competition (not supported by Camp Admin API)
   * @param {Object} competitionData - Competition data
   * @returns {Promise<Object>} Create response
   */
  async createCompetition(competitionData) {
    // Always use mock for create operations
    logApiCall('POST', '/competitions (mock)', competitionData, null)
    return mockCompetitionService.createCompetition(competitionData)
  }

  /**
   * Update competition (not supported by Camp Admin API)
   * @param {string} id - Competition ID
   * @param {Object} competitionData - Competition data
   * @returns {Promise<Object>} Update response
   */
  async updateCompetition(id, competitionData) {
    // Always use mock for update operations
    logApiCall('PUT', `/competitions/${id} (mock)`, competitionData, null)
    return mockCompetitionService.updateCompetition(id, competitionData)
  }

  /**
   * Delete competition (not supported by Camp Admin API)
   * @param {string} id - Competition ID
   * @returns {Promise<Object>} Delete response
   */
  async deleteCompetition(id) {
    // Always use mock for delete operations
    logApiCall('DELETE', `/competitions/${id} (mock)`, null, null)
    return mockCompetitionService.deleteCompetition(id)
  }

  /**
   * Join competition (not supported by Camp Admin API)
   * @param {string} competitionId - Competition ID
   * @param {Object} joinData - Join data
   * @returns {Promise<Object>} Join response
   */
  async joinCompetition(competitionId, joinData) {
    // Always use mock for join operations
    logApiCall('POST', `/competitions/${competitionId}/join (mock)`, joinData, null)
    return mockCompetitionService.joinCompetition(competitionId, joinData)
  }

  /**
   * Get all qualifications (across all competitions)
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} All qualifications response
   */
  async getAllQualifications(params = {}) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', '/qualifications (mock)', params, null)
      return mockCompetitionService.getAllQualifications(params)
    }

    try {
      logApiCall('GET', '/camp-admin/qualifications', params, null)

      const qualificationsResponse = await campAdminApi.getQualifications(params)

      if (!qualificationsResponse.success) {
        throw new Error(qualificationsResponse.error?.message || 'Failed to fetch qualifications')
      }

      logApiCall('GET', '/camp-admin/qualifications', params, qualificationsResponse)
      return qualificationsResponse

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', '/camp-admin/qualifications', params, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Get qualifications for a competition
   * @param {string} competitionId - Competition ID
   * @returns {Promise<Object>} Qualifications response
   */
  async getQualifications(competitionId) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('GET', `/qualifications?competition=${competitionId} (mock)`, null, null)
      return mockCompetitionService.getQualifications(competitionId)
    }

    try {
      logApiCall('GET', `/camp-admin/qualifications?competition_id=${competitionId}`, null, null)

      const qualificationsResponse = await campAdminApi.getQualifications({
        competition_id: competitionId
      })

      if (!qualificationsResponse.success) {
        throw new Error(qualificationsResponse.error?.message || 'Failed to fetch qualifications')
      }

      logApiCall('GET', `/camp-admin/qualifications?competition_id=${competitionId}`, null, qualificationsResponse)
      return qualificationsResponse

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('GET', `/camp-admin/qualifications?competition_id=${competitionId}`, null, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Create qualification
   * @param {Object} qualificationData - Qualification data
   * @returns {Promise<Object>} Create response
   */
  async createQualification(qualificationData) {
    // Use mock service if enabled
    if (isMockApiEnabled()) {
      logApiCall('POST', '/qualifications (mock)', qualificationData, null)
      return mockCompetitionService.createQualification(qualificationData)
    }

    try {
      logApiCall('POST', '/camp-admin/qualifications', qualificationData, null)

      const response = await campAdminApi.createQualification(qualificationData)

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create qualification')
      }

      const result = {
        success: true,
        data: response.data,
        message: 'Qualification created successfully'
      }

      logApiCall('POST', '/camp-admin/qualifications', qualificationData, result)
      return result

    } catch (error) {
      const normalizedError = handleApiError(error)
      logApiCall('POST', '/camp-admin/qualifications', qualificationData, normalizedError)
      throw normalizedError
    }
  }

  /**
   * Update qualification (not supported by Camp Admin API)
   * @param {string} id - Qualification ID
   * @param {Object} qualificationData - Qualification data
   * @returns {Promise<Object>} Update response
   */
  async updateQualification(id, qualificationData) {
    // Always use mock for update operations
    logApiCall('PUT', `/qualifications/${id} (mock)`, qualificationData, null)
    return mockCompetitionService.updateQualification(id, qualificationData)
  }

  /**
   * Delete qualification (not supported by Camp Admin API)
   * @param {string} id - Qualification ID
   * @returns {Promise<Object>} Delete response
   */
  async deleteQualification(id) {
    // Always use mock for delete operations
    logApiCall('DELETE', `/qualifications/${id} (mock)`, null, null)
    return mockCompetitionService.deleteQualification(id)
  }

  /**
   * Sort competitions array
   * @param {Array} competitions - Competitions array
   * @param {string} sortBy - Sort field
   * @param {string} sortOrder - Sort order
   * @returns {Array} Sorted competitions
   */
  sortCompetitions(competitions, sortBy, sortOrder = 'asc') {
    return competitions.sort((a, b) => {
      let aVal = a[sortBy]
      let bVal = b[sortBy]

      // Handle nested properties
      if (sortBy === 'participants' || sortBy === 'submissions') {
        aVal = a.stats?.[sortBy] || 0
        bVal = b.stats?.[sortBy] || 0
      }

      // Handle dates
      if (sortBy === 'startDate' || sortBy === 'endDate' || sortBy === 'createdAt') {
        aVal = new Date(aVal || 0)
        bVal = new Date(bVal || 0)
      }

      // Handle string comparison
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }

      // Sort logic
      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1
      }
      return aVal > bVal ? 1 : -1
    })
  }

  /**
   * Enhance competitions with statistics
   * @param {Array} competitions - Competitions array
   * @returns {Promise<Array>} Enhanced competitions
   */
  async enhanceCompetitionsWithStats(competitions) {
    // For now, return competitions as-is
    // In the future, could fetch individual stats for each competition
    return competitions.map(competition => ({
      ...competition,
      stats: competition.stats || {
        totalParticipants: 0,
        totalSubmissions: 0,
        averageScore: 0,
        completionRate: 0
      }
    }))
  }

  /**
   * Enhance single competition with statistics
   * @param {Object} competition - Competition object
   * @returns {Promise<Object>} Enhanced competition
   */
  async enhanceCompetitionWithStats(competition) {
    try {
      // Use new dedicated analytics endpoint for competition-specific stats
      const stats = await analyticsApi.getCompetitionStats(competition.id)

      return {
        ...competition,
        stats: stats.success ? stats.data : competition.stats || {}
      }
    } catch (error) {
      console.warn(`Failed to enhance competition ${competition.id} with stats:`, error)
      // Return competition with default stats if enhancement fails
      return {
        ...competition,
        stats: competition.stats || {
          totalParticipants: 0,
          totalSubmissions: 0,
          averageScore: 0,
          completionRate: 0
        }
      }
    }
  }

  /**
   * Enhance competition with qualifications
   * @param {Object} competition - Competition object
   * @returns {Promise<Object>} Enhanced competition
   */
  async enhanceCompetitionWithQualifications(competition) {
    try {
      // Get qualifications for this competition using real API
      const qualificationsResponse = await this.getQualifications(competition.id)

      return {
        ...competition,
        qualifications: qualificationsResponse.success ? qualificationsResponse.data : []
      }
    } catch (error) {
      // Return competition without qualifications if enhancement fails
      return {
        ...competition,
        qualifications: []
      }
    }
  }
}

// Create and export service instance
export const competitionService = new CompetitionService()

// Export individual methods for convenience
export const {
  getCompetitions,
  getCompetition,
  getCompetitionStats,
  getRoutes,
  createCompetition,
  updateCompetition,
  deleteCompetition,
  joinCompetition,
  getAllQualifications,
  getQualifications,
  createQualification,
  updateQualification,
  deleteQualification
} = competitionService
