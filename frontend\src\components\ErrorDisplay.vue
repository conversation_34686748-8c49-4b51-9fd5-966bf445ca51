<template>
  <div class="error-display">
    <!-- Simple Error <PERSON> -->
    <el-alert
      v-if="!detailed"
      :title="title"
      :description="message"
      :type="alertType"
      :closable="closable"
      :show-icon="showIcon"
      @close="$emit('close')"
    />
    
    <!-- Detailed Error Card -->
    <el-card v-else class="error-card" :class="`error-${severity}`">
      <template #header>
        <div class="error-header">
          <el-icon class="error-icon" :class="`icon-${severity}`">
            <WarningFilled v-if="severity === 'critical'" />
            <Warning v-else-if="severity === 'high'" />
            <InfoFilled v-else-if="severity === 'medium'" />
            <QuestionFilled v-else />
          </el-icon>
          <span class="error-title">{{ title }}</span>
          <el-button
            v-if="closable"
            type="text"
            size="small"
            @click="$emit('close')"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div class="error-content">
        <p class="error-message">{{ message }}</p>
        
        <!-- Error Details -->
        <el-collapse v-if="hasDetails" v-model="activeCollapse">
          <el-collapse-item title="Error Details" name="details">
            <div class="error-details">
              <div v-if="errorCode" class="detail-item">
                <strong>Error Code:</strong> {{ errorCode }}
              </div>
              <div v-if="errorStatus" class="detail-item">
                <strong>Status:</strong> {{ errorStatus }}
              </div>
              <div v-if="errorDetails" class="detail-item">
                <strong>Details:</strong>
                <pre class="error-json">{{ JSON.stringify(errorDetails, null, 2) }}</pre>
              </div>
              <div v-if="timestamp" class="detail-item">
                <strong>Time:</strong> {{ formatTimestamp(timestamp) }}
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        
        <!-- Action Buttons -->
        <div v-if="showActions" class="error-actions">
          <el-button
            v-if="canRetry"
            type="primary"
            size="small"
            :loading="retrying"
            @click="handleRetry"
          >
            <el-icon><Refresh /></el-icon>
            Retry
          </el-button>
          
          <el-button
            v-if="canReport"
            type="default"
            size="small"
            @click="handleReport"
          >
            <el-icon><Warning /></el-icon>
            Report Issue
          </el-button>
          
          <el-button
            v-if="canGoBack"
            type="default"
            size="small"
            @click="handleGoBack"
          >
            <el-icon><ArrowLeft /></el-icon>
            Go Back
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  WarningFilled,
  Warning,
  InfoFilled,
  QuestionFilled,
  Close,
  Refresh,
  ArrowLeft
} from '@element-plus/icons-vue'

const props = defineProps({
  // Basic props
  title: {
    type: String,
    default: 'Error'
  },
  message: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'error',
    validator: (value) => ['error', 'warning', 'info'].includes(value)
  },
  severity: {
    type: String,
    default: 'medium',
    validator: (value) => ['low', 'medium', 'high', 'critical'].includes(value)
  },
  
  // Display options
  detailed: {
    type: Boolean,
    default: false
  },
  closable: {
    type: Boolean,
    default: true
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: false
  },
  
  // Error information
  errorCode: {
    type: String,
    default: null
  },
  errorStatus: {
    type: Number,
    default: null
  },
  errorDetails: {
    type: Object,
    default: null
  },
  timestamp: {
    type: Date,
    default: () => new Date()
  },
  
  // Action options
  canRetry: {
    type: Boolean,
    default: false
  },
  canReport: {
    type: Boolean,
    default: false
  },
  canGoBack: {
    type: Boolean,
    default: false
  },
  
  // Retry function
  retryFunction: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['close', 'retry', 'report', 'go-back'])

const router = useRouter()
const activeCollapse = ref([])
const retrying = ref(false)

// Computed properties
const alertType = computed(() => {
  const typeMap = {
    error: 'error',
    warning: 'warning',
    info: 'info'
  }
  return typeMap[props.type] || 'error'
})

const hasDetails = computed(() => {
  return props.errorCode || props.errorStatus || props.errorDetails
})

// Methods
const formatTimestamp = (timestamp) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(timestamp)
}

const handleRetry = async () => {
  if (!props.retryFunction) {
    emit('retry')
    return
  }
  
  retrying.value = true
  try {
    await props.retryFunction()
    emit('close')
  } catch (error) {
    console.error('Retry failed:', error)
  } finally {
    retrying.value = false
  }
}

const handleReport = () => {
  emit('report', {
    title: props.title,
    message: props.message,
    errorCode: props.errorCode,
    errorStatus: props.errorStatus,
    errorDetails: props.errorDetails,
    timestamp: props.timestamp
  })
}

const handleGoBack = () => {
  emit('go-back')
  router.go(-1)
}
</script>

<style scoped>
.error-display {
  margin: 16px 0;
}

.error-card {
  border-left: 4px solid var(--el-color-error);
}

.error-card.error-critical {
  border-left-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}

.error-card.error-high {
  border-left-color: var(--el-color-error);
  background-color: var(--el-color-error-light-9);
}

.error-card.error-medium {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.error-card.error-low {
  border-left-color: var(--el-color-info);
  background-color: var(--el-color-info-light-9);
}

.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 20px;
}

.error-icon.icon-critical {
  color: var(--el-color-danger);
}

.error-icon.icon-high {
  color: var(--el-color-error);
}

.error-icon.icon-medium {
  color: var(--el-color-warning);
}

.error-icon.icon-low {
  color: var(--el-color-info);
}

.error-title {
  font-weight: 600;
  flex: 1;
}

.error-content {
  padding: 0;
}

.error-message {
  margin: 0 0 16px 0;
  font-size: 14px;
  line-height: 1.5;
}

.error-details {
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.error-json {
  background-color: var(--el-fill-color-light);
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 4px;
  overflow-x: auto;
}

.error-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
