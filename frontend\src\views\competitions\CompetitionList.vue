<template>
  <div class="competition-list">
    <div class="page-header">
      <h1>活动查看</h1>
      <div class="header-actions">
        <el-button @click="loadData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <!-- <el-button type="primary" @click="$router.push('/competitions/create')">
          <el-icon><Plus /></el-icon>
          Create Competition
        </el-button> -->
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards" v-if="stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalCompetitions || 0 }}</div>
                <div class="stat-label">活动总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon participants">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(stats.totalParticipants) || 0 }}</div>
                <div class="stat-label">总报名人数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeCompetitions || 0 }}</div>
                <div class="stat-label">总通关人数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon upcoming">
                <el-icon><ColdDrink /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.upcomingCompetitions || 0 }}</div>
                <div class="stat-label">总学分获得</div>
              </div>
            </div>
          </el-card>
        </el-col>


      </el-row>
    </div>

    <!-- Filters and Search -->
    <el-card style="margin-top: 20px;">
      <div class="filters-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="filters.search"
              placeholder="搜索活动..."
              clearable
              @input="debouncedSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>

          <el-col :span="4">
            <el-select v-model="filters.status" placeholder="活动状态" clearable @change="loadCompetitions">
              <el-option label="All" value="" />
              <el-option label="Active" value="active" />
              <el-option label="Upcoming" value="upcoming" />
              <el-option label="Completed" value="completed" />
              <el-option label="Draft" value="draft" />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select v-model="filters.routeId" placeholder="赛道" clearable @change="loadCompetitions">
              <el-option
                v-for="route in routes"
                :key="route.id"
                :label="route.name || route.route_name || 'Unknown Route'"
                :value="route.id"
              />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select v-model="filters.sortBy" placeholder="Sort by" @change="loadCompetitions">
              <el-option label="名称" value="title" />
              <el-option label="状态" value="status" />
              <el-option label="活动开始日期" value="startDate" />
              <el-option label="活动总报名数" value="participants" />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-select v-model="filters.sortOrder" placeholder="Order" @change="loadCompetitions">
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- Advanced Filters -->
    <AdvancedFilters
      :show-date-filter="true"
      :show-status-filter="true"
      :show-category-filter="true"
      :show-credit-filter="false"
      :show-location-filter="false"
      :status-options="statusOptions"
      :category-options="routeOptions"
      :custom-filters="customFilters"
      :auto-apply="true"
      @filter-change="handleAdvancedFilterChange"
      @filter-apply="handleAdvancedFilterApply"
      @filter-reset="handleAdvancedFilterReset"
    />

    <!-- Competitions Table -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>活动列表</span>
          <el-tag>{{ pagination.total }} total</el-tag>
        </div>
      </template>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <el-alert
          :title="error"
          type="error"
          show-icon
          :closable="false"
        />
        <el-button @click="loadCompetitions" type="primary" style="margin-top: 16px">
          Retry
        </el-button>
      </div>

      <!-- Competitions Table -->
      <div v-else>
        <el-table
          :data="competitions"
          style="width: 100%"
          v-loading="competitionsLoading"
        >
          <el-table-column prop="title" label="活动名称" min-width="200">
            <template #default="{ row }">
              <div class="competition-cell">
                <div class="competition-name">{{ getDisplayName(row) }}</div>
                <div class="competition-route">{{ row.routeName || 'Unknown Route' }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="活动状态" width="120">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(getDisplayStatus(row))"
                size="small"
              >
                {{ getDisplayStatus(row) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="报名总数" width="120">
            <template #default="{ row }">
              {{ getDisplayParticipants(row) }}
            </template>
          </el-table-column>

          <el-table-column label="提交总数" width="120">
            <template #default="{ row }">
              {{ getDisplaySubmissions(row) }}
            </template>
          </el-table-column>

          <el-table-column prop="startDate" label="活动开始时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.startDate) }}
            </template>
          </el-table-column>

          <el-table-column prop="endDate" label="活动结束时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.endDate) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="manageQualifications(row)"
              >
                规则管理
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="manageCredits(row)"
              >
                学分管理
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="filters.page"
            v-model:page-size="filters.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadCompetitions"
            @current-change="loadCompetitions"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  Trophy,
  VideoPlay,
  Clock,
  User,
  SuccessFilled,
  ColdDrink
} from '@element-plus/icons-vue'
import { competitionService } from '@/services/competitionService'
import { debounce } from 'lodash-es'
import AdvancedFilters from '@/components/common/AdvancedFilters.vue'

const router = useRouter()

// State
const loading = ref(true)
const competitionsLoading = ref(false)
const error = ref(null)
const competitions = ref([])
const routes = ref([])
const stats = ref(null)

// Filters and pagination
const filters = reactive({
  page: 1,
  limit: 20,
  search: '',
  status: '',
  routeId: '',
  sortBy: 'title',
  sortOrder: 'asc'
})

const pagination = reactive({
  total: 0,
  pages: 0,
  hasNext: false,
  hasPrev: false
})

// Advanced filter options
const statusOptions = [
  { label: '全部', value: '' },
  { label: '进行中', value: 'active' },
  { label: '即将开始', value: 'upcoming' },
  { label: '已结束', value: 'completed' },
  { label: '草稿', value: 'draft' },
  { label: '已取消', value: 'cancelled' }
]

const routeOptions = ref([])

const customFilters = [
  {
    key: 'participantRange',
    label: '参与人数',
    component: 'el-slider',
    span: 8,
    props: {
      range: true,
      min: 0,
      max: 1000,
      step: 10,
      showInput: true
    }
  }
]

// Methods - Updated to use backend display fields with fallbacks
const formatNumber = (num) => {
  // Fallback for older data that doesn't have display fields
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString) => {
  // Fallback for older data that doesn't have display fields
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString()
  } catch (error) {
    return 'Invalid Date'
  }
}

const getStatusType = (status) => {
  const statusTypes = {
    'active': 'success',
    'upcoming': 'warning',
    'completed': 'info',
    'draft': 'info',
    'cancelled': 'danger',
    '进行中': 'success',
    '即将开始': 'warning',
    '已结束': 'info'
  }
  return statusTypes[status] || 'info'
}

// Helper functions to use display fields with fallbacks
const getDisplayName = (competition) => {
  return competition.display_name || competition.title || competition.name || 'Unknown Competition'
}

const getDisplayStatus = (competition) => {
  return competition.display_status || competition.status || 'Unknown'
}

const getDisplayParticipants = (competition) => {
  return competition.display_participant_count || formatNumber(competition.participants || 0)
}

const getDisplaySubmissions = (competition) => {
  return competition.display_submission_count || formatNumber(competition.submissions || 0)
}

// Advanced filter handlers
const handleAdvancedFilterChange = (advancedFilters) => {
  // Merge advanced filters with current filters
  Object.assign(filters, advancedFilters)
  loadCompetitions()
}

const handleAdvancedFilterApply = (advancedFilters) => {
  handleAdvancedFilterChange(advancedFilters)
}

const handleAdvancedFilterReset = () => {
  // Reset only advanced filter fields, keep basic search and pagination
  const basicFields = ['page', 'limit', 'search', 'status', 'routeId', 'sortBy', 'sortOrder']
  Object.keys(filters).forEach(key => {
    if (!basicFields.includes(key)) {
      delete filters[key]
    }
  })
  loadCompetitions()
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load statistics and routes in parallel
    const [statsResponse, routesResponse] = await Promise.all([
      competitionService.getCompetitionStats(),
      competitionService.getRoutes({ limit: 100, offset:0 })
    ])

    if (statsResponse.success) {
      stats.value = statsResponse.data
    }

    if (routesResponse.success) {
      const arr = routesResponse.data?.routes || response.routes;
      if (!Array.isArray(arr)) {
        console.warn('接口返回的 routes 不是数组，找不到比赛列表');
        routeOptions.value = [];
        return;
}
      routes.value = routesResponse.data
      console.log(routes)

      // Update route options for advanced filters
      routeOptions.value = routes.data.map(route => ({
        label: route.display_name || route.name || route.route_name || 'Unknown Route',
        value: route.id
      }))
    }

    // Load competitions
    await loadCompetitions()

  } catch (err) {
    console.error('Failed to load competition data:', err)
    error.value = err.message || 'Failed to load data'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

const loadCompetitions = async () => {
  competitionsLoading.value = true

  try {
    const response = await competitionService.getCompetitions(filters)

    if (response.success) {
      competitions.value = response.data
      pagination.total = response.pagination?.total || 0
      pagination.pages = response.pagination?.pages || 0
      pagination.hasNext = response.pagination?.hasNext || false
      pagination.hasPrev = response.pagination?.hasPrev || false
    } else {
      throw new Error(response.error?.message || 'Failed to load competitions')
    }
  } catch (err) {
    console.error('Failed to load competitions:', err)
    ElMessage.error(err.message || 'Failed to load competitions')
  } finally {
    competitionsLoading.value = false
  }
}

const manageQualifications = (competition) => {
  // Navigate to qualification management for this competition
  router.push(`/competitions/${competition.id}/qualifications`)
}

const manageCredits = (competition) => {
  // Navigate to credit management filtered by this competition
  router.push(`/credits?competition=${competition.id}`)
}

// Debounced search
const debouncedSearch = debounce(() => {
  filters.page = 1 // Reset to first page
  loadCompetitions()
}, 500)

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: white;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.active {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.upcoming {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.participants {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stat-info {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #7f8c8d;
          margin-top: 4px;
        }
      }
    }
  }
}

.filters-section {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.competition-cell {
  .competition-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
  }

  .competition-route {
    font-size: 12px;
    color: #7f8c8d;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style>
