#!/usr/bin/env python3
"""
Test script to verify the complete authentication flow is working
"""

import requests
import json

def test_backend_endpoints():
    """Test all backend authentication endpoints"""
    
    print("=== Testing Backend Authentication Endpoints ===")
    
    # Test 1: Login endpoint
    login_url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    credentials = {
        "email": "<EMAIL>",
        "password": "Dick920815##"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:3000"
    }
    
    try:
        print("1. Testing login endpoint...")
        response = requests.post(login_url, json=credentials, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return False
        
        login_data = response.json()
        token = login_data['data']['token']
        print("✅ Login successful")
        
        # Test 2: Token verification endpoint
        print("2. Testing token verification...")
        verify_url = "http://localhost:8005/api/v1/camp-admin/auth/verify"
        verify_headers = {
            "Authorization": f"Bearer {token}",
            "Origin": "http://localhost:3000"
        }
        
        verify_response = requests.get(verify_url, headers=verify_headers)
        if verify_response.status_code != 200:
            print(f"❌ Token verification failed: {verify_response.status_code}")
            return False
        
        print("✅ Token verification successful")
        
        # Test 3: Protected endpoint access
        print("3. Testing protected endpoint access...")
        protected_url = "http://localhost:8005/api/v1/camp-admin/routes"
        
        protected_response = requests.get(protected_url, headers=verify_headers)
        if protected_response.status_code != 200:
            print(f"❌ Protected endpoint failed: {protected_response.status_code}")
            return False
        
        print("✅ Protected endpoint access successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    
    print("\n=== Testing CORS Configuration ===")
    
    # Test preflight request
    url = "http://localhost:8005/api/v1/camp-admin/auth/login"
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "POST",
        "Access-Control-Request-Headers": "Content-Type"
    }
    
    try:
        response = requests.options(url, headers=headers)
        
        if response.status_code != 200:
            print(f"❌ CORS preflight failed: {response.status_code}")
            return False
        
        # Check CORS headers
        cors_headers = {
            'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
            'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
            'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
        }
        
        print("✅ CORS preflight successful")
        print(f"  Allow-Origin: {cors_headers['access-control-allow-origin']}")
        print(f"  Allow-Methods: {cors_headers['access-control-allow-methods']}")
        print(f"  Allow-Credentials: {cors_headers['access-control-allow-credentials']}")
        
        return True
        
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def test_frontend_server():
    """Test if frontend server is running"""
    
    print("\n=== Testing Frontend Server ===")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        
        if response.status_code == 200:
            print("✅ Frontend server is running on port 3000")
            return True
        else:
            print(f"❌ Frontend server returned status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Frontend server is not running on port 3000")
        return False
    except Exception as e:
        print(f"❌ Frontend server test failed: {e}")
        return False

def summarize_fixes():
    """Summarize the fixes that were applied"""
    
    print("\n=== Summary of Applied Fixes ===")
    print("1. ✅ Added CORS middleware to FastAPI backend")
    print("   - Added CORSMiddleware import")
    print("   - Configured CORS with settings from config")
    print("   - Allows requests from http://localhost:3000")
    
    print("\n2. ✅ Fixed authApi.login response structure")
    print("   - Added missing refresh_token field")
    print("   - Added missing expires_in field")
    print("   - Added missing permissions and roles fields")
    print("   - Now returns complete auth data for userAdapter")
    
    print("\n3. ✅ Fixed authApi.refreshToken response structure")
    print("   - Added missing expires_in field")
    print("   - Ensures token refresh works correctly")
    
    print("\n4. ✅ Changed Login.vue redirect method")
    print("   - Replaced window.location.href with router.push")
    print("   - Prevents page reload and auth state loss")
    print("   - Maintains auth store state during navigation")
    
    print("\n=== Expected Result ===")
    print("The login flow should now work correctly:")
    print("1. User enters credentials and clicks login")
    print("2. Frontend sends request to backend with CORS headers")
    print("3. Backend authenticates and returns complete auth data")
    print("4. Frontend stores auth data in store, cookies, and localStorage")
    print("5. Frontend redirects to dashboard using Vue Router")
    print("6. Router guard recognizes authenticated state")
    print("7. User successfully accesses the dashboard")

if __name__ == "__main__":
    print("🧪 Testing Complete Authentication Flow")
    print("=" * 60)
    
    # Run all tests
    backend_ok = test_backend_endpoints()
    cors_ok = test_cors_configuration()
    frontend_ok = test_frontend_server()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"  Backend Endpoints: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"  CORS Configuration: {'✅ PASS' if cors_ok else '❌ FAIL'}")
    print(f"  Frontend Server: {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    
    if backend_ok and cors_ok and frontend_ok:
        print("\n🎉 All tests passed! The authentication flow should work correctly.")
        print("\n📝 Next Steps:")
        print("1. Open http://localhost:3000/login in your browser")
        print("2. Enter credentials: <EMAIL> / Dick920815##")
        print("3. Click login - you should be redirected to the dashboard")
        print("4. Refresh the page - you should remain logged in")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
    
    summarize_fixes()
